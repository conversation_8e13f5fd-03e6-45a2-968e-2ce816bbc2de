/**
 * LiveKit风水AI助手 - 前端图片上传功能
 * 基于LiveKit官方开发指南实现
 */

import { Room, RoomEvent, LocalParticipant } from 'livekit-client';

class FengshuiImageUploader {
    constructor(room) {
        this.room = room;
        this.setupImageUpload();
    }

    setupImageUpload() {
        // 创建图片上传按钮
        const uploadButton = document.createElement('button');
        uploadButton.textContent = '📷 上传房屋图片分析';
        uploadButton.className = 'upload-btn';
        uploadButton.onclick = () => this.triggerImageUpload();
        
        // 创建隐藏的文件输入
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.style.display = 'none';
        fileInput.onchange = (e) => this.handleImageUpload(e);
        
        // 添加到页面
        document.body.appendChild(uploadButton);
        document.body.appendChild(fileInput);
        
        this.uploadButton = uploadButton;
        this.fileInput = fileInput;
        
        console.log('✅ 图片上传功能已初始化');
    }

    triggerImageUpload() {
        this.fileInput.click();
    }

    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        console.log(`📷 准备上传图片: ${file.name}, 大小: ${file.size} bytes`);
        
        // 显示上传状态
        this.uploadButton.textContent = '📤 正在上传...';
        this.uploadButton.disabled = true;

        try {
            // 读取文件为ArrayBuffer
            const arrayBuffer = await this.readFileAsArrayBuffer(file);
            
            // 使用LiveKit ByteStream发送图片 - 基于官方文档
            await this.room.localParticipant.sendFile(
                arrayBuffer,
                'images',  // 对应Agent中注册的topic
                {
                    filename: file.name,
                    mimeType: file.type
                }
            );
            
            console.log('✅ 图片上传成功');
            this.showUploadSuccess();
            
        } catch (error) {
            console.error('❌ 图片上传失败:', error);
            this.showUploadError(error);
        } finally {
            // 恢复按钮状态
            this.uploadButton.textContent = '📷 上传房屋图片分析';
            this.uploadButton.disabled = false;
        }
    }

    readFileAsArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsArrayBuffer(file);
        });
    }

    showUploadSuccess() {
        // 显示成功提示
        const toast = document.createElement('div');
        toast.className = 'toast success';
        toast.textContent = '✅ 图片上传成功！张大师正在分析中...';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }

    showUploadError(error) {
        // 显示错误提示
        const toast = document.createElement('div');
        toast.className = 'toast error';
        toast.textContent = `❌ 图片上传失败: ${error.message}`;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 5000);
    }
}

// 使用示例
async function initializeFengshuiApp() {
    const room = new Room();
    
    // 连接到LiveKit房间
    await room.connect('wss://kjh-a5mlk6sq.livekit.cloud', 'your-token');
    
    // 初始化图片上传功能
    const imageUploader = new FengshuiImageUploader(room);
    
    console.log('🏮 风水AI助手前端已初始化');
}

// CSS样式
const styles = `
.upload-btn {
    background: linear-gradient(135deg, #d4af37, #ffd700);
    color: #8b0000;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.upload-btn:hover {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

.upload-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.toast.success {
    background: #4caf50;
}

.toast.error {
    background: #f44336;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
`;

// 添加样式到页面
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

export { FengshuiImageUploader, initializeFengshuiApp };
