#!/usr/bin/env python3
"""
LiveKit风水AI助手 - 简化工作版本
基于之前正确配置的备份版本
"""

import asyncio
import os
import json
from dotenv import load_dotenv

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
)
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 知识库加载失败: {e}")
        return {}

FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()

@function_tool
async def get_fengshui_advice(question: str):
    """获取风水建议的工具函数"""
    # 简单的关键词匹配
    advice_map = {
        "客厅": "客厅应保持明亮整洁，沙发背后要有实墙作为靠山，避免正对大门。可在东南角放置绿植招财。",
        "卧室": "卧室床头要靠实墙，避免床头对着门或窗。选择温暖色调，保持空气流通。",
        "办公室": "办公桌应面向门口，背后有实墙。桌面保持整洁，可放置文昌塔或绿植提升事业运。",
        "厨房": "厨房要保持清洁，炉灶不要正对水槽。可在厨房放置红色或橙色装饰增强火元素。",
        "颜色": "根据五行理论，东方宜绿色，南方宜红色，西方宜白色，北方宜黑色，中央宜黄色。"
    }
    
    for keyword, advice in advice_map.items():
        if keyword in question:
            return {"advice": advice, "keyword": keyword}
    
    return {"advice": "请提供更具体的问题，比如关于客厅、卧室、办公室等具体空间的风水咨询。", "keyword": "通用"}

@function_tool
async def get_direction_advice(direction: str):
    """获取方位建议的工具函数"""
    direction_map = {
        "东": "东方属木，宜放置绿色植物，有利于健康和成长。",
        "南": "南方属火，宜用红色装饰，有利于名声和事业。",
        "西": "西方属金，宜用白色或金色，有利于贵人运。",
        "北": "北方属水，宜用蓝色或黑色，有利于事业发展。",
        "东南": "东南方为财位，宜保持明亮整洁，可放置招财植物。",
        "西南": "西南方主感情，宜用粉色或红色装饰。",
        "东北": "东北方为文昌位，宜放置书籍或文昌塔。",
        "西北": "西北方主贵人，宜保持整洁，可放置金属装饰。"
    }
    
    advice = direction_map.get(direction, f"关于{direction}方位，建议根据五行理论进行布置。")
    return {"advice": advice, "direction": direction}

@function_tool
async def get_color_advice(color: str):
    """获取颜色风水建议的工具函数"""
    color_map = {
        "红色": "红色属火，象征热情和活力，适合南方或客厅使用，但不宜过多。",
        "绿色": "绿色属木，象征生机和健康，适合东方或书房使用。",
        "蓝色": "蓝色属水，象征智慧和冷静，适合北方或书房使用。",
        "白色": "白色属金，象征纯洁和简约，适合西方或卧室使用。",
        "黄色": "黄色属土，象征稳定和财富，适合中央位置或餐厅使用。",
        "黑色": "黑色属水，象征深沉和神秘，适合北方，但不宜大面积使用。"
    }
    
    advice = color_map.get(color, f"关于{color}的风水建议，请咨询具体的使用场景。")
    return {"advice": advice, "color": color}

class FengshuiAgent(Agent):
    """风水AI助手 - 简化版本"""
    
    def __init__(self) -> None:
        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师。

🎯 你的专长：住宅风水、商业风水、方位布局、色彩搭配

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释风水概念
- 给出具体可行的建议

💡 回答原则：
- 每次回答控制在200字以内，简洁有力
- 可以使用工具获取详细的专业建议
- 始终保持专业和友善的态度

请用中文与用户交流，提供专业的风水咨询服务。
""",
            tools=[get_fengshui_advice, get_direction_advice, get_color_advice],
        )

async def entrypoint(ctx: JobContext):
    """Agent入口点 - 简化版本"""
    await ctx.connect()  # 关键：必须先连接到房间
    print("🏮 启动风水AI助手...")
    print(f"🎯 Agent会话开始: {ctx.room.name}")

    # 初始化语音识别 (Deepgram)
    stt = deepgram.STT(
        model="nova-2-general",
        language="zh",
        smart_format=True,
    )
    print("✅ Deepgram STT初始化成功")

    # 初始化语音合成 (使用Cartesia TTS - 中文语音合成)
    tts = cartesia.TTS(
        model="sonic-2",
        voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 中文女声
        language="zh",
    )
    print("✅ Cartesia TTS初始化成功")

    # 初始化语音活动检测
    vad = silero.VAD.load()
    print("✅ Silero VAD初始化成功")
    
    # 使用DeepSeek LLM - 正确的配置方式
    from livekit.plugins import openai

    deepseek_llm = openai.LLM(
        model="deepseek-chat",
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        base_url="https://api.deepseek.com/v1",
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功")
    
    # 创建Agent会话
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,
        tts=tts,
        vad=vad,
    )
    
    # 启动会话
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )
    
    print("🎯 风水AI助手已就绪，等待用户交互")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
