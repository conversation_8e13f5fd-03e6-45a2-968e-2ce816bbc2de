
> agent-starter-react@0.1.0 dev /www/wwwroot/su.guiyunai.fun/livekit-frontend
> next dev --turbopack -p 7000 -p 7000

 ⨯ Failed to start server
Error: listen EADDRINUSE: address already in use :::7000
    at <unknown> (Error: listen EADDRINUSE: address already in use :::7000)
    at new Promise (<anonymous>) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 7000
}
[?25h
  ○ Compiling / ...

[2m[503.17ms][22m [@tailwindcss/postcss] app/globals.css
[2m[  0.25ms][22m  [2m ↳ [22mQuick bail check
[2m[208.60ms][22m  [2m ↳ [22mSetup compiler
[2m[  2.40ms][22m    [2m ↳ [22mPostCSS AST -> Tailwind CSS AST
[2m[206.09ms][22m    [2m ↳ [22mCreate compiler
[2m[  0.27ms][22m  [2m ↳ [22mRegister full rebuild paths
[2m[ 10.21ms][22m  [2m ↳ [22mSetup scanner
[2m[ 54.57ms][22m  [2m ↳ [22mScan for candidates
[2m[ 10.69ms][22m  [2m ↳ [22mRegister dependency messages
[2m[136.40ms][22m  [2m ↳ [22mBuild utilities
[2m[ 50.01ms][22m  [2m ↳ [22mTransform Tailwind CSS AST into PostCSS AST
[2m[ 30.50ms][22m  [2m ↳ [22mUpdate PostCSS AST


[2m[0.10ms][22m [@tailwindcss/postcss] app/commitmono_fb1c42e5.module.css
[2m[0.07ms][22m  [2m ↳ [22mQuick bail check


[2m[0.04ms][22m [@tailwindcss/postcss] app/commitmono_fb1c42e5.module.css
[2m[0.03ms][22m  [2m ↳ [22mQuick bail check

 ✓ Compiled / in 24.8s
Local storage is not available.
Local storage is not available.
 GET / 200 in 27726ms
Local storage is not available.
 GET / 200 in 597ms
 ○ Compiling /favicon.ico ...
 ✓ Compiled /favicon.ico in 1011ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 1437ms
Local storage is not available.
 HEAD / 200 in 376ms
 ELIFECYCLE  Command failed.
[?25h
pack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

   ▲ Next.js 15.4.5 (Turbopack)
   - Local:        http://localhost:7000
   - Network:      http://***************:7000
   - Environments: .env.local

 ✓ Starting...
Creating turbopack project {
  dir: '/www/wwwroot/su.guiyunai.fun/livekit-frontend',
  testMode: true
}
 ✓ Ready in 3.4s
