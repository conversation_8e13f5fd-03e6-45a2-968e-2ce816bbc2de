nohup: ignoring input

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

 ⨯ Failed to start server
Error: listen EADDRINUSE: address already in use :::7000
    at <unknown> (Error: listen EADDRINUSE: address already in use :::7000)
    at new Promise (<anonymous>) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 7000
}
[?25h
 HEAD / 200 in 242ms
 GET / 200 in 268ms
 GET /api/connection-details 200 in 117ms
 GET /api/connection-details 200 in 121ms
 GET /api/connection-details 200 in 249ms
 GET /api/connection-details 200 in 78ms
 GET / 200 in 289ms
 GET /api/connection-details 200 in 83ms
 GET /api/connection-details 200 in 86ms
 GET / 200 in 326ms
 GET /api/connection-details 200 in 76ms
 ○ Compiling /_not-found/page ...
 ✓ Compiled /_not-found/page in 955ms
 GET /api/v1/users 404 in 1330ms
 GET /api/users 404 in 130ms
 GET /.env 404 in 133ms
 GET /api/connection-details 200 in 77ms
 GET /api/connection-details 200 in 100ms
 GET /api/connection-details 200 in 91ms
 GET /api/connection-details 200 in 72ms
[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h
