
> agent-starter-react@0.1.0 start /www/wwwroot/su.guiyunai.fun/livekit-frontend
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...
 ✓ Ready in 2.3s
 ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:7000". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
 ELIFECYCLE  Command failed.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.
Local storage is not available.

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000

   ▲ Next.js 15.4.5
   - Local:        http://localhost:7000
   - Network:      http://***************:7000

 ✓ Starting...

> agent-starter-react@0.1.0 start
> next start -p 7000


> agent-starter-react@0.1.0 start
> next start -p 7000


> agent-starter-react@0.1.0 start
> next start -p 7000


> agent-starter-react@0.1.0 start
> next start -p 7000


> agent-starter-react@0.1.0 start
> next start -p 7000


> agent-starter-react@0.1.0 start
> next start -p 7000


> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

   ▲ Next.js 15.4.5 (Turbopack)
   - Local:        http://localhost:7000
   - Network:      http://***************:7000
   - Environments: .env.local

 ✓ Starting...
Creating turbopack project {
  dir: '/www/wwwroot/su.guiyunai.fun/livekit-frontend',
  testMode: true
}
 ✓ Ready in 3s
[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

   ▲ Next.js 15.4.5 (Turbopack)
   - Local:        http://localhost:7000
   - Network:      http://***************:7000
   - Environments: .env.local

 ✓ Starting...
Creating turbopack project {
  dir: '/www/wwwroot/su.guiyunai.fun/livekit-frontend',
  testMode: true
}
 ✓ Ready in 3.2s
 ○ Compiling / ...
 ✓ Compiled / in 19.1s
 GET / 200 in 21413ms
 ○ Compiling /favicon.ico ...
 GET /favicon.ico?favicon.2685530d.ico 200 in 2469ms
 ✓ Compiled /favicon.ico in 2.7s
 GET /api/connection-details 200 in 2916ms
 GET /api/connection-details 200 in 62ms
 GET / 200 in 262ms
 GET /api/connection-details 200 in 131ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 352ms
 GET /api/connection-details 200 in 83ms
 GET / 200 in 270ms
 GET /api/connection-details 200 in 89ms
 GET /api/connection-details 200 in 108ms
 GET /api/connection-details 200 in 87ms
 GET /api/connection-details 200 in 102ms
 ○ Compiling /_not-found/page ...
 ✓ Compiled /_not-found/page in 1124ms
 GET /admin/assets/js/pbxlib.js 404 in 1543ms
 GET /api/connection-details 200 in 102ms
 GET / 200 in 355ms
 GET /api/connection-details 200 in 87ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 268ms
 GET /api/connection-details 200 in 60ms
 GET /api/connection-details 200 in 91ms
 GET /api/connection-details 200 in 153ms
 GET /.well-known/security.txt 404 in 183ms
 GET /api/connection-details 200 in 74ms
 GET /api/connection-details 200 in 54ms
 GET /api/connection-details 200 in 86ms
 GET /api/connection-details 200 in 65ms
 GET /api/connection-details 200 in 87ms
 GET /api/connection-details 200 in 81ms
 GET /api/connection-details 200 in 94ms
 GET / 200 in 335ms
 GET /api/connection-details 200 in 72ms
 GET /favicon.ico 200 in 261ms
 GET / 200 in 356ms
 GET /api/connection-details 200 in 72ms
 GET /api/connection-details 200 in 492ms
 GET /robots.txt 404 in 214ms
 GET / 200 in 252ms
 GET /api/connection-details 200 in 95ms
   Reload env: .env.local
 GET / 200 in 3586ms
 GET / 200 in 250ms
 GET /api/connection-details 200 in 292ms
 GET /api/connection-details 200 in 72ms
 GET /api/connection-details 200 in 67ms
 GET /api/connection-details 200 in 107ms
 GET / 200 in 1467ms
 GET /api/connection-details 200 in 98ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 417ms
 GET /api/connection-details 200 in 81ms
 GET /.env 404 in 412ms
 GET /.env 404 in 200ms
 GET / 200 in 311ms
 GET /api/connection-details 200 in 116ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 295ms
 GET /api/connection-details 200 in 1221ms
 GET /api/connection-details 200 in 114ms
 GET /api/connection-details 200 in 68ms
 GET / 200 in 378ms
 GET /api/connection-details 200 in 109ms
 GET /api/connection-details 200 in 91ms
 GET / 200 in 295ms
 GET /admin/assets/js/views/login.js 404 in 177ms
 GET /api/connection-details 200 in 76ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 262ms
 GET / 200 in 322ms
 GET /favicon.ico 200 in 506ms
 GET / 200 in 319ms
 GET /api/connection-details 200 in 306ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 514ms
 GET /api/connection-details 200 in 154ms
 GET /api/connection-details 200 in 102ms
 GET /api/connection-details 200 in 124ms
 GET / 200 in 367ms
 GET / 200 in 241ms
 GET /api/connection-details 200 in 93ms
 GET /api/connection-details 200 in 105ms
 GET /api/connection-details 200 in 107ms
 GET / 200 in 427ms
 GET /api/connection-details 200 in 79ms
 GET /api/connection-details 200 in 95ms
 GET /api/connection-details 200 in 91ms
 GET /api/connection-details 200 in 78ms
 GET /api/connection-details 200 in 126ms
 GET /api/connection-details 200 in 99ms
 GET /api/connection-details 200 in 193ms
 GET /api/connection-details 200 in 200ms
 GET /api/connection-details 200 in 130ms
 GET /api/connection-details 200 in 123ms
 GET /api/connection-details 200 in 74ms
 GET /api/connection-details 200 in 146ms
 GET /api/connection-details 200 in 104ms
 GET /api/connection-details 200 in 78ms
 GET / 200 in 415ms
 GET /api/connection-details 200 in 130ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 334ms
 GET /api/connection-details 200 in 124ms
 GET /api/connection-details 200 in 113ms
 GET /api/connection-details 200 in 92ms
 GET /api/connection-details 200 in 113ms
 GET /api/connection-details 200 in 79ms
 GET /api/connection-details 200 in 63ms
 GET /api/connection-details 200 in 96ms
 GET /api/connection-details 200 in 79ms
 GET /api/connection-details 200 in 103ms
 GET /api/connection-details 200 in 85ms
 GET / 200 in 285ms
 GET /api/connection-details 200 in 76ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 260ms
 GET / 200 in 329ms
 GET / 200 in 258ms
 GET /api/connection-details 200 in 66ms
 GET /api/connection-details 200 in 88ms
 GET / 200 in 420ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 303ms
 GET /api/connection-details 200 in 125ms
 GET /api/connection-details 200 in 138ms
 GET /api/connection-details 200 in 67ms
 GET /api/connection-details 200 in 118ms
 GET / 200 in 323ms
 GET /api/connection-details 200 in 109ms
 GET /api/connection-details 200 in 72ms
 GET /api/connection-details 200 in 76ms
 GET / 200 in 343ms
 GET /api/connection-details 200 in 75ms
 GET /favicon.ico 200 in 263ms
 GET / 200 in 282ms
 GET / 200 in 291ms
 GET /api/connection-details 200 in 67ms
 GET / 200 in 346ms
 GET /api/connection-details 200 in 106ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 323ms
 GET / 200 in 391ms
 GET /favicon.ico 200 in 290ms
 GET / 200 in 336ms
 GET /api/connection-details 200 in 79ms
 GET /api/connection-details 200 in 111ms
 GET /api/connection-details 200 in 90ms
 GET /api/connection-details 200 in 99ms
 GET /api/connection-details 200 in 84ms
 GET / 200 in 427ms
 GET /api/connection-details 200 in 135ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 319ms
 GET /.env 404 in 196ms
 GET /api/connection-details 200 in 86ms
 GET /api/connection-details 200 in 82ms
 GET /api/connection-details 200 in 80ms
 GET / 200 in 327ms
 GET /api/connection-details 200 in 62ms
 GET /api/connection-details 200 in 110ms
 GET /api/connection-details 200 in 65ms
 GET /api/connection-details 200 in 91ms
 GET /api/connection-details 200 in 70ms
 GET /api/connection-details 200 in 62ms
 GET /api/connection-details 200 in 72ms
 GET /api/connection-details 200 in 71ms
 GET /api/connection-details 200 in 84ms
 GET /api/connection-details 200 in 79ms
 GET /api/connection-details 200 in 77ms
 GET / 200 in 319ms
 GET /api/connection-details 200 in 110ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 293ms
 GET /api/connection-details 200 in 119ms
 GET /api/connection-details 200 in 76ms
 GET / 200 in 310ms
 GET /api/connection-details 200 in 78ms
 GET /api/connection-details 200 in 70ms
 GET /api/connection-details 200 in 100ms
 GET / 200 in 282ms
 GET / 200 in 315ms
 GET /api/connection-details 200 in 58ms
 GET /api/connection-details 200 in 63ms
 GET /api/connection-details 200 in 96ms
 GET /api/connection-details 200 in 84ms
 GET /api/connection-details 200 in 95ms
 GET /api/connection-details 200 in 93ms
 GET /api/connection-details 200 in 69ms
 GET /api/connection-details 200 in 98ms
 GET /api/connection-details 200 in 109ms
 GET /api/connection-details 200 in 87ms
 GET /api/connection-details 200 in 61ms
 GET /api/connection-details 200 in 83ms
 GET / 200 in 321ms
 GET /api/connection-details 200 in 80ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 291ms
 GET /api/connection-details 200 in 73ms
 GET / 200 in 284ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 406ms
 GET / 200 in 382ms
 GET /api/connection-details 200 in 94ms
 GET /favicon.ico 200 in 308ms
 GET /api/connection-details 200 in 73ms
 GET /api/connection-details 200 in 72ms
 GET / 200 in 304ms
 GET /api/connection-details 200 in 68ms
 GET /api/connection-details 200 in 72ms
 GET /api/connection-details 200 in 92ms
 GET /api/connection-details 200 in 75ms
 GET /api/connection-details 200 in 89ms
 GET /api/connection-details 200 in 89ms
 GET /api/connection-details 200 in 97ms
 GET /api/connection-details 200 in 62ms
 GET /api/connection-details 200 in 67ms
 GET / 200 in 362ms
 GET /api/connection-details 200 in 89ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 279ms
 GET /api/connection-details 200 in 51ms
 GET /api/connection-details 200 in 78ms
 GET /api/connection-details 200 in 78ms
 GET /api/connection-details 200 in 90ms
 GET /api/connection-details 200 in 85ms
 GET / 200 in 328ms
 GET /api/connection-details 200 in 63ms
 GET /api/connection-details 200 in 71ms
 GET /api/connection-details 200 in 103ms
 GET /.git/index 404 in 217ms
 GET /api/connection-details 200 in 94ms
 GET / 200 in 315ms
 GET /api/connection-details 200 in 81ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 288ms
 GET /api/connection-details 200 in 100ms
 GET / 200 in 321ms
 GET /api/connection-details 200 in 84ms
 GET /api/connection-details 200 in 110ms
 GET /api/connection-details 200 in 307ms
 GET /api/connection-details 200 in 122ms
 GET /api/connection-details 200 in 108ms
 GET /api/connection-details 200 in 101ms
 GET /api/connection-details 200 in 93ms
 GET / 200 in 345ms
 GET /api/connection-details 200 in 92ms
 GET /api/connection-details 200 in 73ms
 GET /api/connection-details 200 in 59ms
 GET / 200 in 236ms
 GET /api/connection-details 200 in 79ms
 GET /api/connection-details 200 in 93ms
 GET /api/connection-details 200 in 188ms
 GET / 200 in 340ms
 GET /api/connection-details 200 in 69ms
 GET /api/connection-details 200 in 103ms
 GET /api/connection-details 200 in 81ms
 GET /api/connection-details 200 in 96ms
 GET /api/connection-details 200 in 91ms
 GET /api/connection-details 200 in 97ms
 GET /api/connection-details 200 in 94ms
 GET /api/connection-details 200 in 134ms
 GET /api/connection-details 200 in 85ms
 GET /api/connection-details 200 in 94ms
 GET /api/connection-details 200 in 65ms
 GET /api/connection-details 200 in 79ms
 GET / 200 in 388ms
 GET /api/connection-details 200 in 83ms
 GET /api/connection-details 200 in 71ms
 GET /api/connection-details 200 in 94ms
 GET / 200 in 482ms
 GET /api/connection-details 200 in 96ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 300ms
 GET /api/connection-details 200 in 72ms
 GET / 200 in 306ms
 GET /api/connection-details 200 in 69ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 269ms
 GET /api/connection-details 200 in 59ms
 GET /api/connection-details 200 in 120ms
 GET /api/connection-details 200 in 115ms
 GET /favicon.ico 200 in 320ms
 GET / 200 in 381ms
 GET /api/connection-details 200 in 134ms
 GET /api/connection-details 200 in 95ms
 GET /api/connection-details 200 in 117ms
 ✓ Compiled in 2.2s
 ✓ Compiled in 0ms
 GET /api/connection-details 200 in 85ms
 GET /api/connection-details 200 in 89ms
 GET /api/connection-details 200 in 104ms
 GET /api/connection-details 200 in 69ms
 GET /api/connection-details 200 in 57ms
 GET /api/connection-details 200 in 362ms
 GET /api/connection-details 200 in 55ms
 GET /api/connection-details 200 in 93ms
 GET /api/connection-details 200 in 88ms
 GET /api/connection-details 200 in 95ms
 GET /api/connection-details 200 in 59ms
 GET /api/connection-details 200 in 92ms
 GET /api/connection-details 200 in 66ms
 GET /api/connection-details 200 in 58ms
 GET /api/connection-details 200 in 63ms
 GET /api/connection-details 200 in 67ms
 GET /api/connection-details 200 in 57ms
 ✓ Compiled in 498ms
 ✓ Compiled in 0ms
 GET /api/connection-details 200 in 70ms
 GET /api/connection-details 200 in 62ms
 GET /api/connection-details 200 in 58ms
 GET /api/connection-details 200 in 72ms
 GET /api/connection-details 200 in 65ms
 GET /api/connection-details 200 in 59ms
 GET /api/connection-details 200 in 50ms
 GET /api/connection-details 200 in 55ms
 GET /api/connection-details 200 in 59ms
 GET /api/connection-details 200 in 61ms
 GET /api/connection-details 200 in 58ms
 GET /api/connection-details 200 in 77ms
 GET /api/connection-details 200 in 51ms
 GET /api/connection-details 200 in 90ms
 GET /api/connection-details 200 in 96ms
 GET /api/connection-details 200 in 709ms
 GET /api/connection-details 200 in 136ms
 GET /api/connection-details 200 in 85ms
 GET /api/connection-details 200 in 83ms
 GET /api/connection-details 200 in 81ms
 GET /api/connection-details 200 in 73ms
 GET /api/connection-details 200 in 84ms
 GET /api/connection-details 200 in 74ms
 GET /api/connection-details 200 in 56ms
 GET /api/connection-details 200 in 83ms
 GET / 200 in 1976ms
 GET /api/connection-details 200 in 76ms
 GET / 200 in 285ms
 GET / 200 in 307ms
 GET /api/connection-details 200 in 99ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 290ms
 GET / 200 in 333ms
 GET /api/connection-details 200 in 69ms
 GET /api/connection-details 200 in 111ms
 GET /api/connection-details 200 in 80ms
 GET /api/connection-details 200 in 65ms
 GET /api/connection-details 200 in 70ms
 GET /api/connection-details 200 in 53ms
 GET /api/connection-details 200 in 74ms
 GET /api/connection-details 200 in 73ms
 GET /api/connection-details 200 in 67ms
 GET /api/connection-details 200 in 76ms
 GET /favicon.ico 200 in 283ms
 GET / 200 in 300ms
 GET / 200 in 322ms
 GET /api/connection-details 200 in 71ms
 GET /api/connection-details 200 in 141ms
 GET / 200 in 392ms
 GET /api/connection-details 200 in 75ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 279ms
 GET / 200 in 366ms
 GET /favicon.ico 200 in 297ms
 GET / 200 in 294ms
 GET /api/connection-details 200 in 81ms
 GET /api/connection-details 200 in 147ms
 GET /api/connection-details 200 in 105ms
 GET /api/connection-details 200 in 127ms
 GET /api/connection-details 200 in 141ms
 GET /api/connection-details 200 in 108ms
 GET /api/connection-details 200 in 115ms
 GET /api/connection-details 200 in 90ms
 GET /api/connection-details 200 in 65ms
 ✓ Compiled in 507ms
 ✓ Compiled in 0ms
 GET /api/connection-details 200 in 127ms
 GET /api/connection-details 200 in 139ms
 GET /api/connection-details 200 in 92ms
 GET /api/connection-details 200 in 65ms
 GET /api/connection-details 200 in 65ms
 GET /api/connection-details 200 in 55ms
 GET /api/connection-details 200 in 70ms
 GET /api/connection-details 200 in 78ms
 GET /api/connection-details 200 in 102ms
 GET /api/connection-details 200 in 75ms
 GET /api/connection-details 200 in 66ms
 GET /api/connection-details 200 in 51ms
 GET /api/connection-details 200 in 66ms
 GET /api/connection-details 200 in 65ms
 GET /api/connection-details 200 in 64ms
 GET /api/connection-details 200 in 69ms
 GET /api/connection-details 200 in 85ms
 GET /api/connection-details 200 in 93ms
 GET / 200 in 2076ms
 GET /api/connection-details 200 in 88ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 289ms
 GET /api/connection-details 200 in 132ms
 GET / 200 in 352ms
 GET /api/connection-details 200 in 106ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 296ms
 GET / 200 in 463ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 279ms
 GET /favicon.ico 200 in 283ms
 GET /api/connection-details 200 in 76ms
 GET /.well-known/security.txt 404 in 275ms
 GET /api/connection-details 200 in 116ms
 GET /api/connection-details 200 in 87ms
 GET /api/connection-details 200 in 90ms
 GET /api/connection-details 200 in 108ms
 GET /api/connection-details 200 in 93ms
 GET /api/connection-details 200 in 112ms
 GET /api/connection-details 200 in 92ms
 GET /api/connection-details 200 in 93ms
 GET / 200 in 380ms
 GET /api/connection-details 200 in 103ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 315ms
 GET /favicon.ico 200 in 372ms
 GET / 200 in 421ms
 GET /api/connection-details 200 in 80ms
 GET /favicon.ico 200 in 294ms
 GET / 200 in 361ms
 GET /api/connection-details 200 in 113ms
 GET /api/connection-details 200 in 87ms
 GET /api/connection-details 200 in 95ms
 GET /api/connection-details 200 in 95ms
 GET /api/connection-details 200 in 77ms
 GET /api/connection-details 200 in 95ms
 GET /api/connection-details 200 in 79ms
 GET /api/connection-details 200 in 84ms
 GET /api/connection-details 200 in 113ms
 GET /api/connection-details 200 in 106ms
 GET /api/connection-details 200 in 74ms
 GET /api/connection-details 200 in 75ms
 GET /api/connection-details 200 in 77ms
 GET /api/connection-details 200 in 63ms
 GET /api/connection-details 200 in 112ms
 GET /api/connection-details 200 in 112ms
 GET /api/connection-details 200 in 109ms
 GET / 200 in 355ms
 GET /api/connection-details 200 in 78ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 285ms
 GET /?XDEBUG_SESSION_START=phpstorm 200 in 302ms
 GET /api/connection-details 200 in 148ms
 GET /api/connection-details 200 in 101ms
 GET /api/connection-details 200 in 92ms
 GET /api/connection-details 200 in 108ms
 GET /api/connection-details 200 in 90ms
 GET / 200 in 386ms
 GET /api/connection-details 200 in 66ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 264ms
 GET /api/connection-details 200 in 169ms
 GET / 200 in 456ms
 GET /api/connection-details 200 in 101ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 282ms
 GET /api/connection-details 200 in 123ms
 GET /api/connection-details 200 in 148ms
 GET /api/connection-details 200 in 92ms
 GET /api/connection-details 200 in 84ms
 GET /api/connection-details 200 in 75ms
 GET /api/connection-details 200 in 134ms
 GET /api/connection-details 200 in 99ms
 GET /api/connection-details 200 in 146ms
 GET /api/connection-details 200 in 111ms
 GET /api/connection-details 200 in 103ms
 GET /favicon.ico 200 in 448ms
 GET / 200 in 436ms
 GET /api/connection-details 200 in 100ms
 GET /api/connection-details 200 in 119ms
 GET /api/connection-details 200 in 86ms
 GET /api/connection-details 200 in 72ms
 GET /api/connection-details 200 in 144ms
 GET /api/connection-details 200 in 87ms
 GET /api/connection-details 200 in 198ms
 GET /api/connection-details 200 in 143ms
 GET /api/connection-details 200 in 91ms
 GET /api/connection-details 200 in 94ms
 GET /api/connection-details 200 in 83ms
 GET /api/connection-details 200 in 109ms
 GET / 200 in 383ms
 GET /api/connection-details 200 in 140ms
 GET / 200 in 301ms
 GET /api/connection-details 200 in 76ms
 GET /api/connection-details 200 in 117ms
 GET /favicon.ico 200 in 295ms
 GET / 200 in 389ms
 GET / 200 in 255ms
 GET /api/connection-details 200 in 83ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 287ms
 GET /api/connection-details 200 in 199ms
 GET /api/connection-details 200 in 94ms
 GET /api/connection-details 200 in 89ms
 GET /api/connection-details 200 in 107ms
 GET /api/connection-details 200 in 192ms
 GET / 200 in 456ms
 GET /api/connection-details 200 in 66ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 297ms
 GET /api/connection-details 200 in 145ms
 GET /api/connection-details 200 in 1014ms
 GET / 200 in 1289ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 394ms
 GET /api/connection-details 200 in 99ms
 GET /api/connection-details 200 in 147ms
 GET /api/connection-details 200 in 112ms
 GET /api/connection-details 200 in 106ms
 GET /api/connection-details 200 in 81ms
 GET /api/connection-details 200 in 186ms
 GET / 200 in 330ms
 GET / 200 in 411ms
 GET /actuator/gateway/routes 404 in 229ms
 GET /node_modules/next/dist/lib/metadata/generate/icon-mark.js 404 in 205ms
 GET /node_modules/next/dist/client/components/layout-router.js 404 in 904ms
 GET /node_modules/next/dist/client/components/metadata/metadata-boundary.js 404 in 890ms
 GET /node_modules/next/dist/client/components/builtin/global-error.js 404 in 888ms
 GET /node_modules/next/dist/client/components/metadata/async-metadata.js 404 in 437ms
 GET /node_modules/next/dist/client/components/render-from-template-context.js 404 in 384ms
 GET /node_modules/next/dist/lib/metadata/generate/icon-mark.js 404 in 406ms
 GET /node_modules/next/dist/client/components/layout-router.js 404 in 1013ms
 GET /node_modules/next/dist/client/components/render-from-template-context.js 404 in 992ms
 GET /node_modules/next/dist/client/components/builtin/global-error.js 404 in 1004ms
 GET /node_modules/next/dist/client/components/metadata/metadata-boundary.js 404 in 992ms
 GET /node_modules/next/dist/client/components/metadata/async-metadata.js 404 in 910ms
 GET / 200 in 370ms
 GET / 200 in 410ms
 GET / 200 in 396ms
 GET /v1/models 404 in 437ms
 GET /v1/models 404 in 209ms
 GET /v1/models 404 in 467ms
 GET /favicon.ico 200 in 277ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 251ms
 POST /php-cgi/php-cgi.exe?%ADd+cgi.force_redirect%3D0+%ADd+disable_functions%3D%22%22+%ADd+allow_url_include%3D1+%ADd+auto_prepend_file%3Dphp://input 404 in 274ms
 GET /admin/assets/js/views/login.js 404 in 294ms
 GET / 200 in 371ms
 GET / 200 in 477ms
 GET / 200 in 423ms
 GET / 200 in 389ms
 GET /odinhttpcall1754270495 404 in 359ms
 POST /sdk 404 in 532ms
 GET /evox/about 404 in 236ms
 GET /HNAP1 404 in 303ms
 GET / 200 in 203ms
 GET / 200 in 230ms
 POST / 200 in 231ms
 GET / 200 in 458ms
 POST / 200 in 338ms
 POST / 200 in 295ms
 GET /WuEL 404 in 232ms
 GET /a 404 in 445ms
 GET /download/file.ext 404 in 295ms
 GET /SiteLoader 404 in 183ms
 GET /mPlayer 404 in 197ms
 GET / 200 in 337ms
 POST /hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input 404 in 322ms
 GET /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php 404 in 157ms
 GET /vendor/phpunit/phpunit/Util/PHP/eval-stdin.php 404 in 143ms
 GET /vendor/phpunit/src/Util/PHP/eval-stdin.php 404 in 155ms
 GET /vendor/phpunit/Util/PHP/eval-stdin.php 404 in 381ms
 GET /vendor/phpunit/phpunit/LICENSE/eval-stdin.php 404 in 167ms
 GET /vendor/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php 404 in 135ms
 GET /phpunit/phpunit/src/Util/PHP/eval-stdin.php 404 in 155ms
 GET /phpunit/phpunit/Util/PHP/eval-stdin.php 404 in 142ms
 GET /phpunit/src/Util/PHP/eval-stdin.php 404 in 212ms
 GET /phpunit/Util/PHP/eval-stdin.php 404 in 135ms
 GET /lib/phpunit/phpunit/src/Util/PHP/eval-stdin.php 404 in 130ms
 GET / 200 in 26985ms
 GET /?XDEBUG_SESSION_START=phpstorm 200 in 470ms
 GET /.env 404 in 270ms
 GET / 200 in 428ms
 GET /.git/HEAD 404 in 237ms
 GET /actuator/gateway/routes 404 in 307ms
 GET / 200 in 426ms
 GET / 200 in 372ms
 GET /api/connection-details 200 in 89ms
 GET / 200 in 474ms
 GET / 200 in 364ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 275ms
 GET / 200 in 310ms
 GET /api/connection-details 200 in 184ms
 GET /api/connection-details 200 in 105ms
 GET /admin/assets/js/views/login.js 404 in 343ms
 GET /favicon.ico 200 in 348ms
 GET / 200 in 383ms
 GET /api/connection-details 200 in 114ms
 GET / 200 in 387ms
 GET /api/connection-details 200 in 99ms
 POST /api/connection-details 405 in 157ms
 POST /api/connection-details 405 in 111ms
 GET /api/connection-details 200 in 157ms
 GET /api/connection-details 200 in 131ms
 GET /api/connection-details 200 in 183ms
 GET / 200 in 613ms
 GET /api/connection-details 200 in 124ms
 GET / 200 in 432ms
 GET /api/connection-details 200 in 186ms
 GET / 200 in 475ms
 GET /api/connection-details 200 in 78ms
 GET /api/connection-details 200 in 153ms
 GET /api/connection-details 200 in 225ms
 GET /api/connection-details 200 in 118ms
 GET /api/connection-details 200 in 82ms
 ○ Compiling /_error ...
 ✓ Compiled in 9.3s
 ✓ Compiled /_error in 7.5s
