
> agent-starter-react@0.1.0 start /www/wwwroot/su.guiyunai.fun/livekit-frontend
> next start -p 7000 -p 7000

 ⨯ Failed to start server
Error: listen EADDRINUSE: address already in use :::7000
    at <unknown> (Error: listen EADDRINUSE: address already in use :::7000)
    at new Promise (<anonymous>) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 7000
}
 ELIFECYCLE  Command failed with exit code 1.
[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

   ▲ Next.js 15.4.5 (Turbopack)
   - Local:        http://localhost:7000
   - Network:      http://***************:7000
   - Environments: .env.local

 ✓ Starting...
Creating turbopack project {
  dir: '/www/wwwroot/su.guiyunai.fun/livekit-frontend',
  testMode: true
}
 ✓ Ready in 3.1s
 ○ Compiling /api/connection-details ...
 ✓ Compiled /api/connection-details in 5s
 GET /api/connection-details 200 in 5584ms
 ○ Compiling / ...
 ✓ Compiled / in 15.9s
 GET / 200 in 7585ms
 GET / 200 in 17806ms
 ○ Compiling /favicon.ico ...
 GET /api/connection-details 200 in 86ms
 ✓ Compiled /favicon.ico in 567ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 990ms
 GET / 200 in 309ms
 GET /api/connection-details 200 in 75ms
