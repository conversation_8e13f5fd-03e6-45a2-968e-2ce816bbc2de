
> agent-starter-react@0.1.0 start /www/wwwroot/su.guiyunai.fun/livekit-frontend
> next start -p 7000 -p 7000

 ⨯ Failed to start server
Error: listen EADDRINUSE: address already in use :::7000
    at <unknown> (Error: listen EADDRINUSE: address already in use :::7000)
    at new Promise (<anonymous>) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 7000
}
 ELIFECYCLE  Command failed with exit code 1.
[?25h

> agent-starter-react@0.1.0 dev
> next dev --turbopack -p 7000

   ▲ Next.js 15.4.5 (Turbopack)
   - Local:        http://localhost:7000
   - Network:      http://***************:7000
   - Environments: .env.local

 ✓ Starting...
Creating turbopack project {
  dir: '/www/wwwroot/su.guiyunai.fun/livekit-frontend',
  testMode: true
}
 ✓ Ready in 3.1s
 ○ Compiling /api/connection-details ...
 ✓ Compiled /api/connection-details in 5s
 GET /api/connection-details 200 in 5584ms
 ○ Compiling / ...
 ✓ Compiled / in 15.9s
 GET / 200 in 7585ms
 GET / 200 in 17806ms
 ○ Compiling /favicon.ico ...
 GET /api/connection-details 200 in 86ms
 ✓ Compiled /favicon.ico in 567ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 990ms
 GET / 200 in 309ms
 GET /api/connection-details 200 in 75ms
 GET /api/connection-details 200 in 109ms
 GET /api/connection-details 200 in 89ms
 GET /api/connection-details 200 in 62ms
 GET / 200 in 329ms
 GET /api/connection-details 200 in 102ms
 GET /api/connection-details 200 in 91ms
 GET / 200 in 321ms
 GET / 200 in 225ms
 GET /api/connection-details 200 in 66ms
 GET /favicon.ico?favicon.2685530d.ico 200 in 260ms
 GET /api/connection-details 200 in 59ms
 GET /favicon.ico 200 in 293ms
 GET / 200 in 285ms
 GET /api/connection-details 200 in 132ms
 GET /api/connection-details 200 in 81ms
 GET / 200 in 297ms
 GET /api/connection-details 200 in 77ms
 GET /api/connection-details 200 in 114ms
 GET /api/connection-details 200 in 185ms
 GET /api/connection-details 200 in 93ms
 ○ Compiling /_not-found/page ...
 ✓ Compiled /_not-found/page in 1292ms
 GET /admin/assets/css/jquery-ui.css 404 in 1635ms
 GET /solr/admin/info/system 404 in 331ms
 GET /cgi-bin/authLogin.cgi 404 in 453ms
 GET /v2/_catalog 404 in 338ms
 GET /query?q=SHOW+DIAGNOSTICS 404 in 454ms
 GET /solr/admin/cores?action=STATUS&wt=json 404 in 151ms
 GET / 200 in 322ms
 GET /api/connection-details 200 in 298ms
 GET /api/connection-details 200 in 71ms
