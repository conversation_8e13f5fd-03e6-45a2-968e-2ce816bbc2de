{"message": "starting worker", "level": "INFO", "name": "livekit.agents", "version": "1.2.2", "rtc-version": "1.0.12", "timestamp": "2025-08-04T05:53:25.589657+00:00"}
{"message": "preloading plugins", "level": "INFO", "name": "livekit.agents", "packages": ["livekit.plugins.deepgram", "livekit.plugins.cartesia", "livekit.plugins.silero", "av"], "timestamp": "2025-08-04T05:53:25.590103+00:00"}
{"message": "worker failed", "level": "ERROR", "name": "livekit.agents", "exc_info": "Traceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/cli/_run.py\", line 79, in _worker_run\n    await worker.run()\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/worker.py\", line 396, in run\n    await self._http_server.start()\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/utils/http_server.py\", line 27, in start\n    self._server = await self._loop.create_server(handler, self._host, self._port)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/asyncio/base_events.py\", line 1572, in create_server\n    raise OSError(err.errno, msg) from None\nOSError: [Errno 98] error while attempting to bind on address ('0.0.0.0', 8081): address already in use", "timestamp": "2025-08-04T05:53:25.599632+00:00"}
{"message": "draining worker", "level": "INFO", "name": "livekit.agents", "id": "unregistered", "timeout": 1800, "timestamp": "2025-08-04T05:53:25.622452+00:00"}
{"message": "shutting down worker", "level": "INFO", "name": "livekit.agents", "id": "unregistered", "timestamp": "2025-08-04T05:53:25.623146+00:00"}
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.f{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.9140251000000001, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T05:54:18.386105+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.5797154999999998, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T05:54:23.396103+00:00"}
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.8075548999999999, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T05:54:25.897848+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.6992926999999998, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T05:54:28.398444+00:00"}
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.8167474, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T05:54:48.406301+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.5815325999999998, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T05:54:50.906933+00:00"}
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.9179497999999997, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T05:55:48.431545+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.6017873999999998, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T05:55:58.434679+00:00"}
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.9093495999999999, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T06:00:06.102255+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.5790017999999998, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T06:00:08.603187+00:00"}
{"message": "received job request", "level": "INFO", "name": "livekit.agents", "job_id": "AJ_9uMhitoohWsN", "dispatch_id": "", "room_name": "voice_assistant_room_4874", "agent_name": "", "resuming": false, "timestamp": "2025-08-04T06:12:08.711725+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 308372, "timestamp": "2025-08-04T06:12:09.006012+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 308372, "elapsed_time": 0.06, "timestamp": "2025-08-04T06:12:09.069221+00:00"}
{"message": "failed to generate LLM completion, retrying in 0.1s\nTraceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/plugins/openai/llm.py\", line 654, in _run\n    self._oai_stream = stream = await self._client.chat.completions.create(\n                                      ^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/functools.py\", line 995, in __get__\n    val = self.func(instance)\n          ^^^^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/openai/_client.py\", line 494, in chat\n    from .resources.chat import AsyncChat\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/openai/resources/__init__.py\", line 67, in <module>\n    from .uploads import (\nModuleNotFoundError: No module named 'openai.resources.uploads'\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/llm/llm.py\", line 174, in _main_task\n    return await self._run()\n           ^^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/plugins/openai/llm.py\", line 699, in _run\n    raise APIConnectionError(retryable=retryable) from e\nlivekit.agents._exceptions.APIConnectionError: Connection error. (body=None, retryable=True)", "level": "WARNING", "name": "livekit.agents", "llm": "livekit.plugins.openai.llm.LLM", "attempt": 1, "pid": 306139, "job_id": "AJ_9uMhitoohWsN", "timestamp": "2025-08-04T06:12:29.378156+00:00"}
