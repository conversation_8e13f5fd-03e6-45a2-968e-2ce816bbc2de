{"message": "starting worker", "level": "INFO", "name": "livekit.agents", "version": "1.2.2", "rtc-version": "1.0.12", "timestamp": "2025-08-04T08:09:49.316822+00:00"}
{"message": "preloading plugins", "level": "INFO", "name": "livekit.agents", "packages": ["livekit.plugins.deepgram", "livekit.plugins.cartesia", "livekit.plugins.silero", "av"], "timestamp": "2025-08-04T08:09:49.317731+00:00"}
{"message": "worker failed", "level": "ERROR", "name": "livekit.agents", "exc_info": "Traceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/cli/_run.py\", line 79, in _worker_run\n    await worker.run()\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/worker.py\", line 396, in run\n    await self._http_server.start()\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/utils/http_server.py\", line 27, in start\n    self._server = await self._loop.create_server(handler, self._host, self._port)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/asyncio/base_events.py\", line 1572, in create_server\n    raise OSError(err.errno, msg) from None\nOSError: [Errno 98] error while attempting to bind on address ('::', 8081, 0, 0): address already in use", "timestamp": "2025-08-04T08:09:49.367549+00:00"}
{"message": "draining worker", "level": "INFO", "name": "livekit.agents", "id": "unregistered", "timeout": 1800, "timestamp": "2025-08-04T08:09:49.423060+00:00"}
{"message": "shutting down worker", "level": "INFO", "name": "livekit.agents", "id": "unregistered", "timestamp": "2025-08-04T08:09:49.423583+00:00"}
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/simple_agent_fixed.py", line 559, in <module>
    cli.run_app(
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/cli/cli.py", line 246, in run_app
    cli()
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/cli/cli.py", line 73, in start
    _run.run_worker(args)
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/cli/_run.py", line 101, in run_worker
    loop.run_until_complete(worker.aclose())
  File "/usr/lib/python3.12/asyncio/base_events.py", line 687, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/worker.py", line 562, in aclose
    assert self._close_future is not None
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     {"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.3545992999999945, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T08:09:40.908630+00:00"}
{"message": "process is unresponsive", "level": "WARNING", "name": "livekit.agents", "delay": 707, "pid": 308372, "timestamp": "2025-08-04T08:11:46.732136+00:00"}
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.7758221999999945, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T08:12:43.553138+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.5903456999999948, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T08:12:48.567688+00:00"}
{"message": "received job request", "level": "INFO", "name": "livekit.agents", "job_id": "AJ_UioE6Q78NGQA", "dispatch_id": "", "room_name": "voice_assistant_room_8562", "agent_name": "", "resuming": false, "timestamp": "2025-08-04T08:13:56.677610+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 491286, "timestamp": "2025-08-04T08:13:57.306763+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 491286, "elapsed_time": 0.38, "timestamp": "2025-08-04T08:13:57.683458+00:00"}
{"message": "failed to generate LLM completion, retrying in 0.1s\nTraceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/plugins/openai/llm.py\", line 654, in _run\n    self._oai_stream = stream = await self._client.chat.completions.create(\n                                      ^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/functools.py\", line 995, in __get__\n    val = self.func(instance)\n          ^^^^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/openai/_client.py\", line 494, in chat\n    from .resources.chat import AsyncChat\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/openai/resources/__init__.py\", line 67, in <module>\n    from .uploads import (\nModuleNotFoundError: No module named 'openai.resources.uploads'\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/llm/llm.py\", line 174, in _main_task\n    return await self._run()\n           ^^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/plugins/openai/llm.py\", line 699, in _run\n    raise APIConnectionError(retryable=retryable) from e\nlivekit.agents._exceptions.APIConnectionError: Connection error. (body=None, retryable=True)", "level": "WARNING", "name": "livekit.agents", "llm": "livekit.plugins.openai.llm.LLM", "attempt": 1, "pid": 306143, "job_id": "AJ_UioE6Q78NGQA", "timestamp": "2025-08-04T08:14:10.528427+00:00"}
{"message": "ignoring byte stream with topic 'images', no callback attached", "level": "INFO", "name": "root", "pid": 306143, "job_id": "AJ_UioE6Q78NGQA", "timestamp": "2025-08-04T08:14:25.047203+00:00"}
{"message": "ignoring byte stream with topic 'images', no callback attached", "level": "INFO", "name": "root", "pid": 306143, "job_id": "AJ_UioE6Q78NGQA", "timestamp": "2025-08-04T08:14:35.330153+00:00"}
