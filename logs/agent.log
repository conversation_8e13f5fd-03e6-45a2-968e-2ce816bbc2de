{"message": "starting worker", "level": "INFO", "name": "livekit.agents", "version": "1.2.2", "rtc-version": "1.0.12", "timestamp": "2025-08-04T08:25:38.743463+00:00"}
{"message": "preloading plugins", "level": "INFO", "name": "livekit.agents", "packages": ["livekit.plugins.deepgram", "livekit.plugins.cartesia", "livekit.plugins.silero", "av"], "timestamp": "2025-08-04T08:25:38.743917+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 492286, "timestamp": "2025-08-04T08:25:41.120970+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 492288, "timestamp": "2025-08-04T08:25:41.126904+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 492290, "timestamp": "2025-08-04T08:25:41.138544+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 492292, "timestamp": "2025-08-04T08:25:41.152295+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 492288, "elapsed_time": 0.11, "timestamp": "2025-08-04T08:25:41.233515+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 492290, "elapsed_time": 0.11, "timestamp": "2025-08-04T08:25:41.251545+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 492286, "elapsed_time": 0.14, "timestamp": "2025-08-04T08:25:41.258661+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 492292, "elapsed_time": 0.14, "timestamp": "2025-08-04T08:25:41.295218+00:00"}
{"message": "registered worker", "level": "INFO", "name": "livekit.agents", "id": "AW_X5brzztrrqyY", "url": "wss://kjh-a5mlk6sq.livekit.cloud", "region": "Singapore", "protocol": 16, "timestamp": "2025-08-04T08:25:42.069590+00:00"}
{"message": "received job request", "level": "INFO", "name": "livekit.agents", "job_id": "AJ_5bPYJCvjynBM", "dispatch_id": "", "room_name": "voice_assistant_room_8597", "agent_name": "", "resuming": false, "timestamp": "2025-08-04T08:28:26.925915+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 492530, "timestamp": "2025-08-04T08:28:27.192199+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 492530, "elapsed_time": 0.06, "timestamp": "2025-08-04T08:28:27.255256+00:00"}
{"message": "failed to generate LLM completion, retrying in 0.1s\nTraceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/plugins/openai/llm.py\", line 654, in _run\n    self._oai_stream = stream = await self._client.chat.completions.create(\n                                      ^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/functools.py\", line 995, in __get__\n    val = self.func(instance)\n          ^^^^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/openai/_client.py\", line 494, in chat\n    from .resources.chat import AsyncChat\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/openai/resources/__init__.py\", line 67, in <module>\n    from .uploads import (\nModuleNotFoundError: No module named 'openai.resources.uploads'\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/llm/llm.py\", line 174, in _main_task\n    return await self._run()\n           ^^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/plugins/openai/llm.py\", line 699, in _run\n    raise APIConnectionError(retryable=retryable) from e\nlivekit.agents._exceptions.APIConnectionError: Connection error. (body=None, retryable=True)", "level": "WARNING", "name": "livekit.agents", "llm": "livekit.plugins.openai.llm.LLM", "attempt": 1, "pid": 492288, "job_id": "AJ_5bPYJCvjynBM", "timestamp": "2025-08-04T08:28:54.649615+00:00"}
