{"message": "starting worker", "level": "INFO", "name": "livekit.agents", "version": "1.2.2", "rtc-version": "1.0.12", "timestamp": "2025-08-04T08:54:52.438673+00:00"}
{"message": "preloading plugins", "level": "INFO", "name": "livekit.agents", "packages": ["livekit.plugins.deepgram", "livekit.plugins.cartesia", "livekit.plugins.silero", "av"], "timestamp": "2025-08-04T08:54:52.439219+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 497458, "timestamp": "2025-08-04T08:54:54.462702+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 497460, "timestamp": "2025-08-04T08:54:54.466862+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 497462, "timestamp": "2025-08-04T08:54:54.476898+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 497464, "timestamp": "2025-08-04T08:54:54.491519+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 497460, "elapsed_time": 0.08, "timestamp": "2025-08-04T08:54:54.544279+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 497462, "elapsed_time": 0.07, "timestamp": "2025-08-04T08:54:54.552244+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 497458, "elapsed_time": 0.1, "timestamp": "2025-08-04T08:54:54.564137+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 497464, "elapsed_time": 0.11, "timestamp": "2025-08-04T08:54:54.603157+00:00"}
{"message": "registered worker", "level": "INFO", "name": "livekit.agents", "id": "AW_Yr8nEfpNtwpc", "url": "wss://kjh-a5mlk6sq.livekit.cloud", "region": "Singapore", "protocol": 16, "timestamp": "2025-08-04T08:54:55.526116+00:00"}
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.8210984000000006, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T08:59:20.660343+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.5325058000000006, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-04T08:59:25.661945+00:00"}
