nohup: ignoring input
{"message": "starting worker", "level": "INFO", "name": "livekit.agents", "version": "1.2.2", "rtc-version": "1.0.12", "timestamp": "2025-08-04T10:27:31.717918+00:00"}
{"message": "preloading plugins", "level": "INFO", "name": "livekit.agents", "packages": ["livekit.plugins.deepgram", "livekit.plugins.cartesia", "livekit.plugins.silero", "av"], "timestamp": "2025-08-04T10:27:31.718242+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 514931, "timestamp": "2025-08-04T10:27:33.489049+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 514933, "timestamp": "2025-08-04T10:27:33.496018+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 514935, "timestamp": "2025-08-04T10:27:33.508205+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 514937, "timestamp": "2025-08-04T10:27:33.510699+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 514933, "elapsed_time": 0.11, "timestamp": "2025-08-04T10:27:33.607412+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 514937, "elapsed_time": 0.11, "timestamp": "2025-08-04T10:27:33.618133+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 514931, "elapsed_time": 0.14, "timestamp": "2025-08-04T10:27:33.632867+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 514935, "elapsed_time": 0.14, "timestamp": "2025-08-04T10:27:33.653126+00:00"}
{"message": "registered worker", "level": "INFO", "name": "livekit.agents", "id": "AW_LweqjabLAnFG", "url": "wss://kjh-a5mlk6sq.livekit.cloud", "region": "Singapore", "protocol": 16, "timestamp": "2025-08-04T10:27:34.458865+00:00"}
{"message": "received job request", "level": "INFO", "name": "livekit.agents", "job_id": "AJ_dEskH8PpFTqs", "dispatch_id": "", "room_name": "voice_assistant_room_7399", "agent_name": "", "resuming": false, "timestamp": "2025-08-04T10:36:25.115319+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 515515, "timestamp": "2025-08-04T10:36:25.317472+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 515515, "elapsed_time": 0.12, "timestamp": "2025-08-04T10:36:25.433457+00:00"}
{"message": "unhandled exception while running the job task\nTraceback (most recent call last):\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/opentelemetry/util/_decorator.py\", line 71, in async_wrapper\n    return await func(*args, **kwargs)  # type: ignore\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/venv/lib/python3.12/site-packages/livekit/agents/ipc/job_proc_lazy_main.py\", line 240, in _traceable_entrypoint\n    await self._job_entrypoint_fnc(job_ctx)\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/pure_deepseek_agent.py\", line 168, in entrypoint\n    deepseek_llm = PureDeepSeekLLM(\n                   ^^^^^^^^^^^^^^^^\n  File \"/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/pure_deepseek_agent.py\", line 78, in __init__\n    self.model = model\n    ^^^^^^^^^^\nAttributeError: property 'model' of 'PureDeepSeekLLM' object has no setter", "level": "ERROR", "name": "livekit.agents", "pid": 514933, "job_id": "AJ_dEskH8PpFTqs", "timestamp": "2025-08-04T10:36:29.997433+00:00"}
{"message": "process exiting", "level": "INFO", "name": "livekit.agents", "reason": "room disconnected", "pid": 514933, "job_id": "AJ_dEskH8PpFTqs", "timestamp": "2025-08-04T10:46:45.750414+00:00"}
{"message": "livekit::rtc_engine:453:livekit::rtc_engine - received session close: \"signal client closed: \\\"stream closed\\\"\" UnknownReason Resume", "level": "WARNING", "name": "livekit", "pid": 514933, "job_id": "AJ_dEskH8PpFTqs", "timestamp": "2025-08-04T10:46:45.749830+00:00"}
🏮 启动风水AI助手 (纯DeepSeek版本)...
🎯 Agent会话开始: voice_assistant_room_7399
✅ Deepgram STT初始化成功
✅ Cartesia TTS初始化成功
✅ Silero VAD初始化成功
{"message": "draining worker", "level": "INFO", "name": "livekit.agents", "id": "AW_LweqjabLAnFG", "timeout": 1800, "timestamp": "2025-08-04T10:51:12.863598+00:00"}
{"message": "shutting down worker", "level": "INFO", "name": "livekit.agents", "id": "AW_LweqjabLAnFG", "timestamp": "2025-08-04T10:51:12.864701+00:00"}
{"message": "process exited with non-zero exit code 255", "level": "ERROR", "name": "livekit.agents", "pid": 514931, "timestamp": "2025-08-04T10:51:12.875634+00:00"}
{"message": "process exited with non-zero exit code 255", "level": "ERROR", "name": "livekit.agents", "pid": 515515, "timestamp": "2025-08-04T10:51:12.877038+00:00"}
{"message": "process exited with non-zero exit code 255", "level": "ERROR", "name": "livekit.agents", "pid": 514935, "timestamp": "2025-08-04T10:51:12.877424+00:00"}
{"message": "process exited with non-zero exit code 255", "level": "ERROR", "name": "livekit.agents", "pid": 514937, "timestamp": "2025-08-04T10:51:12.878517+00:00"}
