#!/bin/bash

# LiveKit风水AI助手部署脚本
# 创建时间: 2025-08-01
# 用途: 一键部署完整的LiveKit风水AI助手项目

set -e  # 遇到错误立即退出

echo "🏮 LiveKit风水AI助手部署脚本"
echo "================================"

# 检查当前目录
if [ ! -d "livekit-fengshui-agent" ] || [ ! -d "livekit-frontend" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 函数: 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ 错误: $1 未安装"
        exit 1
    fi
}

# 检查必要的命令
echo "🔍 检查系统依赖..."
check_command python3
check_command pnpm
check_command node

echo "✅ 系统依赖检查通过"

# 部署后端Agent
echo ""
echo "🔧 部署后端Agent..."
cd livekit-fengshui-agent

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
echo "📦 安装后端依赖..."
source venv/bin/activate
pip install -r requirements.txt

# 下载模型文件
echo "📥 下载AI模型文件..."
python agent.py download-files

echo "✅ 后端Agent部署完成"

# 返回根目录
cd ..

# 部署前端
echo ""
echo "🎨 部署前端界面..."
cd livekit-frontend

# 安装前端依赖
echo "📦 安装前端依赖..."
pnpm install

# 构建前端
echo "🏗️ 构建前端应用..."
pnpm build

echo "✅ 前端界面部署完成"

# 返回根目录
cd ..

# 显示部署结果
echo ""
echo "🎉 部署完成！"
echo "================================"
echo ""
echo "📋 项目结构:"
echo "├── livekit-fengshui-agent/  (后端Agent服务)"
echo "│   ├── agent.py            (主要Agent代码)"
echo "│   ├── .env                (环境变量配置)"
echo "│   └── venv/               (Python虚拟环境)"
echo "└── livekit-frontend/       (前端React应用)"
echo "    ├── .env.local          (前端环境配置)"
echo "    └── .next/              (构建输出)"
echo ""
echo "🚀 启动命令:"
echo ""
echo "1. 启动后端Agent (开发模式):"
echo "   cd livekit-fengshui-agent"
echo "   source venv/bin/activate"
echo "   python agent.py dev"
echo ""
echo "2. 启动前端界面 (开发模式):"
echo "   cd livekit-frontend"
echo "   pnpm dev"
echo ""
echo "3. 启动后端Agent (生产模式):"
echo "   cd livekit-fengshui-agent"
echo "   source venv/bin/activate"
echo "   python agent.py start"
echo ""
echo "4. 启动前端界面 (生产模式):"
echo "   cd livekit-frontend"
echo "   pnpm start"
echo ""
echo "🔑 API密钥状态:"
echo "✅ DeepSeek API: 已配置"
echo "✅ Deepgram API: 已配置"
echo "✅ Cartesia API: 已配置"
echo "✅ LiveKit Cloud: 已配置"
echo ""
echo "🌐 访问地址:"
echo "前端界面: http://localhost:7000"
echo "生产域名: https://su.guiyunai.fun"
echo "后端Agent: 通过LiveKit Cloud连接"
echo ""
echo "📞 如需帮助，请查看项目README文件"
