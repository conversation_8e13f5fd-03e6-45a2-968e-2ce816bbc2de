# 🏮 LiveKit风水AI助手 - 完整项目

基于LiveKit官方架构和DeepSeek AI构建的专业风水咨询系统，包含完整的前端界面和后端Agent服务。

## 🎯 项目特点

- ✅ **官方LiveKit架构** - 使用最新v1.2.2版本，生产级稳定性
- ✅ **完整前后端分离** - React前端 + Python后端Agent
- ✅ **DeepSeek AI集成** - 替代OpenAI，中文理解优秀
- ✅ **专业风水知识** - 30年经验的风水大师人设
- ✅ **实时语音交互** - STT + LLM + TTS完整链路
- ✅ **风水主题定制** - 太极八卦UI设计，专业风水配色
- ✅ **知识库管理** - 可视化Web界面，支持CRUD操作和热更新
- ✅ **性能优化** - LRU缓存、VAD优化、预连接缓冲等多项优化

## 🏗️ 项目架构

```
LiveKit风水AI助手
├── livekit-fengshui-agent/     # 后端Agent服务
│   ├── agent.py               # 主要Agent代码
│   ├── knowledge_base_manager.py    # 知识库管理器
│   ├── knowledge_web_manager.py     # Web管理界面
│   ├── templates/             # Web界面模板
│   ├── fengshui_knowledge.json      # 风水知识库
│   ├── .env                   # 环境变量配置
│   ├── requirements.txt       # Python依赖
│   └── venv/                  # Python虚拟环境
└── livekit-frontend/          # 前端React应用
    ├── app-config.ts          # 风水主题配置
    ├── components/            # React组件
    ├── .env.local            # 前端环境配置
    └── public/               # 静态资源(含风水logo)
```

## 🚀 快速开始

### 一键部署
```bash
# 运行部署脚本
./deploy.sh
```

### 手动部署

#### 1. 后端Agent部署
```bash
cd livekit-fengshui-agent
source venv/bin/activate
pip install -r requirements.txt
python agent.py download-files
```

#### 2. 前端界面部署
```bash
cd livekit-frontend
pnpm install
pnpm build
```

## 🎮 运行项目

### 开发模式
```bash
# 终端1: 启动后端Agent
cd livekit-fengshui-agent
source venv/bin/activate
python agent.py dev

# 终端2: 启动前端界面
cd livekit-frontend
pnpm dev
```

### 生产模式
```bash
# 一键启动生产环境 (推荐)
./start-production.sh

# 或手动启动
# 终端1: 启动后端Agent
cd livekit-fengshui-agent
source venv/bin/activate
python agent.py start

# 终端2: 启动前端界面 (端口7000)
cd livekit-frontend
pnpm start

# 停止服务
./stop-production.sh

# 查看状态
./status.sh
```

## 🔑 API密钥配置

所有必要的API密钥已配置完成：

- ✅ **DeepSeek API**: 已配置，用于AI对话
- ✅ **Deepgram API**: 已配置，用于语音识别
- ✅ **Cartesia API**: 已配置，用于语音合成
- ✅ **LiveKit Cloud**: 已配置，用于实时通信

## 📚 知识库管理

### Web管理界面
启动知识库Web管理界面：
```bash
cd livekit-fengshui-agent
source venv/bin/activate
python knowledge_web_manager.py
```

访问地址：http://localhost:5001

### 主要功能
- 🏠 **房间管理**: 添加、编辑、删除房间风水建议
- 🧭 **方位管理**: 管理八个方位的风水指导
- 🎨 **颜色管理**: 管理各种颜色的风水寓意
- 📥 **导入导出**: 支持JSON格式的数据备份和恢复
- 📊 **实时监控**: 监控系统状态和操作历史
- 🔄 **热更新**: 修改后无需重启Agent，立即生效

### 命令行管理
```bash
# 获取统计信息
python knowledge_base_manager.py get_stats

# 添加房间建议
python knowledge_base_manager.py add_entry room_advice 客厅 '{"basic":"客厅风水建议"}'

# 导出知识库
python knowledge_base_manager.py export backup.json

# 导入知识库
python knowledge_base_manager.py import backup.json false
```

## 🎨 风水主题特色

### UI设计
- 🎯 **太极八卦Logo**: 专业风水符号设计
- 🌈 **风水配色**: 金色(财运) + 红色(吉祥)
- 🏮 **中文界面**: 完全中文化的用户体验

### 功能特色
- 🏠 **住宅风水分析**: 房屋布局、朝向分析
- 🏢 **商业风水咨询**: 办公室、店铺布局
- 🎨 **装饰风水建议**: 颜色、摆设指导
- 💬 **实时语音对话**: 自然语音交流体验

## 🌐 访问地址

- **前端界面**: http://localhost:7000
- **生产域名**: https://su.guiyunai.fun
- **后端Agent**: 通过LiveKit Cloud连接

## 📊 技术栈

### 后端技术
- **LiveKit Agents v1.2.2**: 官方Agent框架
- **DeepSeek API**: 大语言模型
- **Deepgram**: 语音识别(STT)
- **Cartesia**: 语音合成(TTS)
- **Silero VAD**: 语音活动检测

### 前端技术
- **Next.js 15**: React框架
- **LiveKit React SDK**: 实时通信
- **Tailwind CSS**: 样式框架
- **TypeScript**: 类型安全

## 🔧 自定义配置

### 修改风水大师人设
编辑 `livekit-fengshui-agent/.env`:
```bash
FENGSHUI_MASTER_NAME=李大师
FENGSHUI_EXPERTISE=住宅风水,商业风水,择日选址
```

### 修改前端主题
编辑 `livekit-frontend/app-config.ts`:
```typescript
export const APP_CONFIG_DEFAULTS: AppConfig = {
  companyName: '风水AI助手',
  accent: '#d4af37',  // 金色主题
  accentDark: '#ff6b6b',  // 红色主题
  // ...其他配置
};
```

## 🐛 故障排除

### 常见问题

1. **模型下载失败**
   ```bash
   cd livekit-fengshui-agent
   source venv/bin/activate
   python agent.py download-files
   ```

2. **前端依赖安装失败**
   ```bash
   cd livekit-frontend
   rm -rf node_modules pnpm-lock.yaml
   pnpm install
   ```

3. **API连接问题**
   - 检查 `.env` 文件中的API密钥
   - 确认网络连接正常
   - 查看控制台错误日志

## 📄 许可证

本项目基于MIT许可证开源，可自由用于商业用途。

## 📞 技术支持

- **LiveKit官方文档**: https://docs.livekit.io/agents/
- **DeepSeek API文档**: https://platform.deepseek.com/api-docs/
- **项目仓库**: 当前目录

---

**🎉 恭喜！您的LiveKit风水AI助手项目已完整部署！**
