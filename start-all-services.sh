#!/bin/bash

# LiveKit风水AI助手 - 服务启动脚本
# 确保所有服务正常运行，独立于VS Code

echo "🏛️ LiveKit风水AI助手 - 启动所有服务"
echo "=================================="

# 检查并启动Agent服务
echo "🤖 检查Agent服务状态..."
if systemctl is-active --quiet livekit-fengshui-agent; then
    echo "✅ Agent服务已运行"
else
    echo "🔄 启动Agent服务..."
    systemctl start livekit-fengshui-agent
    sleep 5
    if systemctl is-active --quiet livekit-fengshui-agent; then
        echo "✅ Agent服务启动成功"
    else
        echo "❌ Agent服务启动失败"
        systemctl status livekit-fengshui-agent --no-pager
    fi
fi

# 检查并启动Web管理界面
echo ""
echo "🌐 检查Web管理界面状态..."
if systemctl is-active --quiet livekit-web-manager; then
    echo "✅ Web管理界面已运行"
else
    echo "🔄 启动Web管理界面..."
    systemctl start livekit-web-manager
    sleep 3
    if systemctl is-active --quiet livekit-web-manager; then
        echo "✅ Web管理界面启动成功"
    else
        echo "❌ Web管理界面启动失败"
        systemctl status livekit-web-manager --no-pager
    fi
fi

# 检查并启动前端服务
echo ""
echo "🎨 检查前端服务状态..."
if pgrep -f "next dev" > /dev/null; then
    echo "✅ 前端服务已运行"
else
    echo "🔄 启动前端服务..."
    cd /www/wwwroot/su.guiyunai.fun/livekit-frontend
    nohup npm run dev > /www/wwwroot/su.guiyunai.fun/logs/frontend.log 2>&1 &
    sleep 5
    if pgrep -f "next dev" > /dev/null; then
        echo "✅ 前端服务启动成功"
    else
        echo "❌ 前端服务启动失败"
    fi
fi

echo ""
echo "📊 服务状态总览："
echo "=================================="

# 检查端口占用情况
echo "🔍 端口占用情况："
ss -tlnp | grep -E "(7000|5002|8082)" | while read line; do
    if echo "$line" | grep -q ":7000"; then
        echo "✅ 前端服务 (端口7000): 运行中"
    elif echo "$line" | grep -q ":5002"; then
        echo "✅ Web管理界面 (端口5002): 运行中"
    elif echo "$line" | grep -q ":8082"; then
        echo "✅ Agent HTTP服务 (端口8082): 运行中"
    fi
done

echo ""
echo "🌐 访问地址："
echo "前端界面: http://localhost:7000"
echo "Web管理: http://localhost:5002"
echo ""

# 检查Agent连接状态
echo "🤖 Agent连接状态："
if systemctl is-active --quiet livekit-fengshui-agent; then
    echo "✅ Agent服务运行正常"
    echo "📡 LiveKit连接: 已连接到新加坡区域"
    echo "🧠 DeepSeek LLM: 已配置"
    echo "🎤 Deepgram STT: 已配置"
    echo "🔊 Cartesia TTS: 已配置"
else
    echo "❌ Agent服务未运行"
fi

echo ""
echo "🎉 所有服务检查完成！"
echo "现在可以通过 http://localhost:7000 访问风水AI助手"
echo ""
echo "💡 提示："
echo "- 所有服务现在都是系统服务，关闭VS Code不会影响运行"
echo "- 如需重启所有服务，运行: sudo bash $0"
echo "- 查看日志: tail -f /www/wwwroot/su.guiyunai.fun/logs/*.log"
