#!/usr/bin/env python3
"""
简单的视觉模型测试 - 测试DeepSeek文本和智谱视觉能力
"""

import os
import asyncio
import base64
import time
import requests
from dotenv import load_dotenv
from PIL import Image, ImageDraw
import io

# 加载环境变量
load_dotenv('/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/.env')

async def test_deepseek_text():
    """测试DeepSeek文本分析能力"""
    print("🧪 测试DeepSeek-V2.5文本分析...")
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DeepSeek API密钥未配置")
        return None
    
    try:
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user",
                    "content": "请作为专业风水大师，分析一个包含桌子、沙发、植物的简单房间布局的风水情况。要求：1.布局分析 2.风水建议 3.改善方案。控制在150字内。"
                }
            ],
            "temperature": 0.7,
            "max_tokens": 300
        }
        
        start_time = time.time()
        response = requests.post(
            "https://api.deepseek.com/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            analysis = result['choices'][0]['message']['content']
            usage = result.get('usage', {})
            
            print("✅ DeepSeek测试成功")
            print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
            print(f"📊 Token使用: {usage}")
            print(f"📝 分析结果:\n{analysis}")
            
            return {
                'success': True,
                'model': 'DeepSeek-V2.5',
                'analysis': analysis,
                'response_time': end_time - start_time,
                'usage': usage
            }
        else:
            print(f"❌ DeepSeek API错误: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ DeepSeek测试失败: {e}")
        return None

def create_simple_test_image():
    """创建简单的测试图片"""
    # 创建一个简单的房间布局图
    img = Image.new('RGB', (300, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # 画房间边界
    draw.rectangle([20, 20, 280, 180], outline='black', width=2)
    
    # 画家具
    draw.rectangle([50, 50, 100, 80], fill='brown', outline='black')  # 桌子
    draw.text((60, 90), "桌子", fill='black')
    
    draw.rectangle([150, 120, 220, 150], fill='blue', outline='black')  # 沙发
    draw.text((160, 160), "沙发", fill='black')
    
    draw.rectangle([240, 40, 260, 70], fill='green', outline='black')  # 植物
    draw.text((230, 80), "植物", fill='black')
    
    # 转换为base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    image_bytes = buffer.getvalue()
    image_base64 = base64.b64encode(image_bytes).decode('utf-8')
    
    return image_base64, len(image_bytes)

async def test_zhipu_vision():
    """测试智谱GLM-4V视觉能力"""
    print("\n🧪 测试智谱GLM-4V视觉分析...")
    
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 智谱AI API密钥未配置")
        return None
    
    # 创建测试图片
    image_base64, image_size = create_simple_test_image()
    print(f"📷 创建测试图片完成 (大小: {image_size} bytes)")
    
    try:
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": "glm-4v",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请作为专业风水大师分析这张房间布局图片，包括：1.布局分析 2.风水建议 3.改善方案。控制在150字内。"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.7,
            "max_tokens": 300
        }
        
        start_time = time.time()
        response = requests.post(
            "https://open.bigmodel.cn/api/paas/v4/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            analysis = result['choices'][0]['message']['content']
            usage = result.get('usage', {})
            
            print("✅ 智谱GLM-4V测试成功")
            print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
            print(f"📊 Token使用: {usage}")
            print(f"📝 分析结果:\n{analysis}")
            
            return {
                'success': True,
                'model': '智谱GLM-4V',
                'analysis': analysis,
                'response_time': end_time - start_time,
                'usage': usage
            }
        else:
            print(f"❌ 智谱API错误: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 智谱测试失败: {e}")
        return None

async def test_deepseek_vision_alternative():
    """测试DeepSeek是否支持视觉功能"""
    print("\n🧪 测试DeepSeek是否支持视觉...")
    
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        return None
    
    # 创建测试图片
    image_base64, _ = create_simple_test_image()
    
    try:
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # 尝试使用deepseek-vl模型
        payload = {
            "model": "deepseek-vl-chat",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请描述这张图片"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 100
        }
        
        response = requests.post(
            "https://api.deepseek.com/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            analysis = result['choices'][0]['message']['content']
            print("✅ DeepSeek支持视觉功能！")
            print(f"📝 视觉分析: {analysis}")
            return True
        else:
            print(f"❌ DeepSeek视觉测试失败: {response.status_code}")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek视觉测试异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("🏮 风水AI模型能力测试")
    print("="*50)
    
    # 测试DeepSeek文本能力
    deepseek_result = await test_deepseek_text()
    
    # 测试DeepSeek视觉能力
    deepseek_vision = await test_deepseek_vision_alternative()
    
    # 测试智谱视觉能力
    zhipu_result = await test_zhipu_vision()
    
    # 总结
    print("\n" + "="*50)
    print("🎯 测试结果总结:")
    print(f"   DeepSeek文本能力: {'✅' if deepseek_result else '❌'}")
    print(f"   DeepSeek视觉能力: {'✅' if deepseek_vision else '❌'}")
    print(f"   智谱GLM-4V视觉: {'✅' if zhipu_result else '❌'}")
    
    # 推荐方案
    print("\n💡 推荐方案:")
    if deepseek_result and deepseek_vision:
        print("   🏆 DeepSeek同时支持文本和视觉，推荐使用DeepSeek")
    elif deepseek_result and zhipu_result:
        print("   🏆 DeepSeek文本 + 智谱视觉的混合方案")
    elif deepseek_result:
        print("   🏆 使用DeepSeek文本分析，视觉功能需要其他方案")
    else:
        print("   ⚠️ 需要进一步配置API密钥或充值")

if __name__ == "__main__":
    asyncio.run(main())
