#!/usr/bin/env python3
"""
TTS测试脚本 - 比较不同TTS提供商的中文语音效果
"""

import asyncio
import os
from dotenv import load_dotenv

load_dotenv()

# 测试文本
TEST_TEXT = "您好！我是张大师，一位专业的风水顾问。今天我将为您提供专业的风水咨询服务。请告诉我您想了解哪方面的风水知识？"

async def test_azure_tts():
    """测试Azure AI Speech TTS"""
    print("🧪 测试 Azure AI Speech TTS (中文优化)")
    try:
        from livekit.plugins import azure
        
        tts = azure.TTS(
            voice="zh-CN-XiaoxiaoNeural",  # 中文女声
            language="zh-CN"
        )
        
        print("✅ Azure TTS初始化成功")
        print(f"📝 测试文本: {TEST_TEXT}")
        
        # 这里只是测试初始化，实际语音合成需要在LiveKit环境中
        return True
        
    except Exception as e:
        print(f"❌ Azure TTS测试失败: {e}")
        return False

async def test_elevenlabs_tts():
    """测试ElevenLabs TTS"""
    print("\n🧪 测试 ElevenLabs TTS (高质量)")
    try:
        from livekit.plugins import elevenlabs
        
        tts = elevenlabs.TTS(
            model="eleven_multilingual_v2",
            language="zh"
        )
        
        print("✅ ElevenLabs TTS初始化成功")
        print(f"📝 测试文本: {TEST_TEXT}")
        return True
        
    except Exception as e:
        print(f"❌ ElevenLabs TTS测试失败: {e}")
        return False

async def test_deepseek_llm():
    """测试DeepSeek LLM"""
    print("\n🧪 测试 DeepSeek LLM (对话生成)")
    try:
        from livekit.plugins import openai

        llm = openai.LLM.with_deepseek(
            model="deepseek-chat",
            temperature=0.7
        )

        print("✅ DeepSeek LLM初始化成功")
        print(f"📝 测试文本: {TEST_TEXT}")
        return True

    except Exception as e:
        print(f"❌ DeepSeek LLM测试失败: {e}")
        return False

async def test_cartesia_tts():
    """测试Cartesia TTS"""
    print("\n🧪 测试 Cartesia TTS (原始)")
    try:
        from livekit.plugins import cartesia
        
        tts = cartesia.TTS(
            model="sonic-2",
            voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",
            language="zh"
        )
        
        print("✅ Cartesia TTS初始化成功")
        print(f"📝 测试文本: {TEST_TEXT}")
        return True
        
    except Exception as e:
        print(f"❌ Cartesia TTS测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎯 LiveKit TTS 中文语音测试")
    print("=" * 50)
    
    results = []
    
    # 测试正确的AI配置组合
    results.append(("Azure AI Speech", await test_azure_tts()))
    results.append(("ElevenLabs", await test_elevenlabs_tts()))
    results.append(("DeepSeek LLM", await test_deepseek_llm()))
    results.append(("Cartesia TTS", await test_cartesia_tts()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    for name, success in results:
        status = "✅ 可用" if success else "❌ 不可用"
        print(f"  {name}: {status}")
    
    # 推荐配置 (当前正确的配置)
    print("\n🎯 当前正确的AI配置:")
    print("  ✅ STT: Deepgram (中文语音识别)")
    print("  ✅ LLM: DeepSeek Chat (对话生成)")
    print("  ✅ TTS: Cartesia (语音合成)")
    print("  ✅ VAD: Silero (语音活动检测)")

    print("\n💡 环境变量要求:")
    print("  - DEEPGRAM_API_KEY (语音识别)")
    print("  - DEEPSEEK_API_KEY (对话生成)")
    print("  - CARTESIA_API_KEY (语音合成)")
    print("  - LIVEKIT_API_KEY & LIVEKIT_API_SECRET (实时通信)")

if __name__ == "__main__":
    asyncio.run(main())
