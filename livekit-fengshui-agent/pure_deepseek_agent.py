#!/usr/bin/env python3
"""
LiveKit风水AI助手 - 纯DeepSeek版本
完全不使用OpenAI插件，直接使用DeepSeek API
"""

import asyncio
import os
import json
import httpx
from dotenv import load_dotenv

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
)
from livekit.agents.llm import LLM, ChatContext, ChatMessage
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 知识库加载失败: {e}")
        return {}

FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()

@function_tool
async def get_fengshui_advice(question: str):
    """获取风水建议的工具函数"""
    advice_map = {
        "客厅": "客厅应保持明亮整洁，沙发背后要有实墙作为靠山，避免正对大门。可在东南角放置绿植招财。",
        "卧室": "卧室床头要靠实墙，避免床头对着门或窗。选择温暖色调，保持空气流通。",
        "办公室": "办公桌应面向门口，背后有实墙。桌面保持整洁，可放置文昌塔或绿植提升事业运。",
        "厨房": "厨房要保持清洁，炉灶不要正对水槽。可在厨房放置红色或橙色装饰增强火元素。",
        "颜色": "根据五行理论，东方宜绿色，南方宜红色，西方宜白色，北方宜黑色，中央宜黄色。"
    }
    
    for keyword, advice in advice_map.items():
        if keyword in question:
            return {"advice": advice, "keyword": keyword}
    
    return {"advice": "请告诉我您想了解哪个房间或方面的风水，比如客厅、卧室、办公室等。", "keyword": "通用"}

@function_tool
async def get_direction_advice(direction: str):
    """获取方位风水建议"""
    direction_map = {
        "东": "东方属木，宜放置绿色植物，有利于健康和成长。",
        "南": "南方属火，宜用红色装饰，有利于名声和事业。",
        "西": "西方属金，宜用白色或金色，有利于财运。",
        "北": "北方属水，宜用黑色或蓝色，有利于事业发展。",
        "东南": "东南方为财位，宜保持整洁，可放置招财植物。",
        "西南": "西南方为坤位，代表女主人，宜温馨布置。",
        "东北": "东北方为艮位，宜静不宜动，适合学习区域。",
        "西北": "西北方为乾位，代表男主人，宜庄重大气。"
    }
    
    advice = direction_map.get(direction, "请提供具体的方位，如东、南、西、北等。")
    return {"advice": advice, "direction": direction}

class PureDeepSeekLLM(LLM):
    """纯DeepSeek LLM实现，不依赖OpenAI插件"""

    def __init__(self, model: str = "deepseek-chat", temperature: float = 0.7):
        super().__init__()
        self._model_name = model
        self._temperature = temperature
        self.api_key = os.getenv("DEEPSEEK_API_KEY")
        self.base_url = "https://api.deepseek.com/v1"

        if not self.api_key:
            raise ValueError("DEEPSEEK_API_KEY environment variable is required")
    
    async def chat(self, chat_ctx: ChatContext, **kwargs):
        """实现聊天功能"""
        messages = []
        for msg in chat_ctx.messages:
            if isinstance(msg.content, str):
                messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self._model_name,
                    "messages": messages,
                    "temperature": self._temperature,
                    "stream": False
                },
                timeout=30.0
            )
            
            if response.status_code != 200:
                raise Exception(f"DeepSeek API error: {response.status_code} {response.text}")
            
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            
            return ChatMessage(role="assistant", content=content)

class FengshuiAgent(Agent):
    """风水AI助手Agent类"""
    
    def __init__(self):
        super().__init__(
            name="风水大师",
            instructions="""你是一位专业的风水大师，名叫张大师，有30年的风水咨询经验。

你的专长包括：
- 住宅风水布局分析和建议
- 办公室风水优化
- 五行相配和颜色搭配
- 方位选择和摆设建议
- 招财旺运的风水布局

请用温和、专业的语气与用户交流，提供实用的风水建议。
始终保持中文对话，语言要通俗易懂。
""",
            tools=[get_fengshui_advice, get_direction_advice],
        )

async def entrypoint(ctx: JobContext):
    """Agent入口点 - 纯DeepSeek版本"""
    await ctx.connect()
    print("🏮 启动风水AI助手 (纯DeepSeek版本)...")
    print(f"🎯 Agent会话开始: {ctx.room.name}")

    # 初始化语音识别 (Deepgram)
    stt = deepgram.STT(
        model="nova-2-general",
        language="zh",
        smart_format=True,
    )
    print("✅ Deepgram STT初始化成功")

    # 初始化语音合成 (Cartesia)
    tts = cartesia.TTS(
        model="sonic-2",
        voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 中文女声
        language="zh",
    )
    print("✅ Cartesia TTS初始化成功")

    # 初始化语音活动检测 (Silero)
    vad = silero.VAD.load()
    print("✅ Silero VAD初始化成功")
    
    # 使用纯DeepSeek LLM - 不依赖OpenAI插件
    deepseek_llm = PureDeepSeekLLM(
        model="deepseek-chat",
        temperature=0.7
    )
    print("✅ 纯DeepSeek LLM初始化成功")
    
    # 创建Agent会话
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,
        tts=tts,
        vad=vad,
    )
    
    # 启动会话
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )
    
    print("🎯 风水AI助手已就绪，等待用户交互")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
