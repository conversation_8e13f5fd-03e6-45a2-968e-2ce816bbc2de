<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风水AI助手 - 图片上传测试</title>
    <script src="https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .upload-section.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background: #5a6fd8;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .preview {
            max-width: 100%;
            max-height: 300px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        .connect-btn {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        .connect-btn:hover {
            background: #218838;
        }
        .connect-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏮 风水AI助手 - 图片分析测试</h1>
        
        <div class="controls">
            <button id="connectBtn" class="connect-btn">连接到Agent</button>
            <button id="disconnectBtn" class="connect-btn" style="background: #dc3545;" disabled>断开连接</button>
        </div>
        
        <div id="status" class="status info">
            请先连接到Agent，然后上传图片进行风水分析
        </div>
        
        <div class="upload-section" id="uploadSection">
            <p>📷 拖拽图片到这里或点击按钮上传</p>
            <input type="file" id="fileInput" accept="image/*">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择图片
            </button>
            <div id="preview"></div>
        </div>
    </div>

    <script>
        let room = null;
        let connected = false;

        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const status = document.getElementById('status');
        const fileInput = document.getElementById('fileInput');
        const uploadSection = document.getElementById('uploadSection');
        const preview = document.getElementById('preview');

        // LiveKit连接配置
        const LIVEKIT_URL = 'wss://kjh-a5mlk6sq.livekit.cloud';
        const ROOM_NAME = 'fengshui-test-room';
        
        // 生成临时token (生产环境中应该从服务器获取)
        async function generateToken() {
            // 这里应该调用您的服务器API来生成token
            // 为了测试，我们使用一个简单的方法
            const response = await fetch('/api/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    room: ROOM_NAME,
                    identity: 'user-' + Math.random().toString(36).substr(2, 9)
                })
            });
            
            if (!response.ok) {
                throw new Error('Failed to get token');
            }
            
            const data = await response.json();
            return data.token;
        }

        // 连接到LiveKit房间
        async function connect() {
            try {
                updateStatus('正在连接到Agent...', 'info');
                connectBtn.disabled = true;

                room = new LiveKit.Room();
                
                // 监听房间事件
                room.on(LiveKit.RoomEvent.Connected, () => {
                    connected = true;
                    updateStatus('✅ 已连接到风水AI助手！现在可以上传图片了', 'success');
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                });

                room.on(LiveKit.RoomEvent.Disconnected, () => {
                    connected = false;
                    updateStatus('已断开连接', 'info');
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                });

                room.on(LiveKit.RoomEvent.ParticipantConnected, (participant) => {
                    console.log('Participant connected:', participant.identity);
                    if (participant.identity.includes('agent')) {
                        updateStatus('✅ 风水AI助手已就绪！可以上传图片进行分析', 'success');
                    }
                });

                // 获取token并连接
                const token = await generateToken();
                await room.connect(LIVEKIT_URL, token);

            } catch (error) {
                console.error('Connection failed:', error);
                updateStatus('❌ 连接失败: ' + error.message, 'error');
                connectBtn.disabled = false;
            }
        }

        // 断开连接
        async function disconnect() {
            if (room) {
                await room.disconnect();
                room = null;
            }
        }

        // 上传图片
        async function uploadImage(file) {
            if (!connected || !room) {
                updateStatus('❌ 请先连接到Agent', 'error');
                return;
            }

            try {
                updateStatus('📤 正在上传图片...', 'info');

                // 使用LiveKit的sendFile方法上传图片
                const info = await room.localParticipant.sendFile(file, {
                    mimeType: file.type,
                    topic: 'images',  // 这个topic必须与Agent中注册的一致
                    onProgress: (progress) => {
                        updateStatus(`📤 上传进度: ${Math.ceil(progress * 100)}%`, 'info');
                    }
                });

                updateStatus(`✅ 图片上传成功！正在分析中... (ID: ${info.id})`, 'success');
                console.log('File sent with stream ID:', info.id);

            } catch (error) {
                console.error('Upload failed:', error);
                updateStatus('❌ 上传失败: ' + error.message, 'error');
            }
        }

        // 更新状态显示
        function updateStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // 预览图片
        function previewImage(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" class="preview" alt="预览图片">`;
            };
            reader.readAsDataURL(file);
        }

        // 事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                previewImage(file);
                uploadImage(file);
            }
        });

        // 拖拽上传
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    previewImage(file);
                    uploadImage(file);
                } else {
                    updateStatus('❌ 请选择图片文件', 'error');
                }
            }
        });
    </script>
</body>
</html>
