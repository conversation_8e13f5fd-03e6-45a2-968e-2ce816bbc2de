#!/usr/bin/env python3
"""
LiveKit风水AI助手 - 基于官方标准实现
完全遵循LiveKit官方Vision和Byte Streams文档
"""

import asyncio
import base64
import os
import json
from dotenv import load_dotenv

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
    get_job_context,
)
from livekit import rtc
from livekit.agents.llm import ChatMessage, ImageContent, ChatContext
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 知识库加载失败: {e}")
        return {}

FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()

@function_tool
async def get_fengshui_advice(question: str):
    """获取风水建议的工具函数"""
    # 简单的关键词匹配
    advice_map = {
        "客厅": "客厅应保持明亮整洁，沙发背后要有实墙作为靠山，避免正对大门。可在东南角放置绿植招财。",
        "卧室": "卧室床头要靠实墙，避免床头对着门或窗。选择温暖色调，保持空气流通。",
        "办公室": "办公桌应面向门口，背后有实墙。桌面保持整洁，可放置文昌塔或绿植提升事业运。",
        "厨房": "厨房要保持清洁，炉灶不要正对水槽。可在厨房放置红色或橙色装饰增强火元素。",
        "颜色": "根据五行理论，东方宜绿色，南方宜红色，西方宜白色，北方宜黑色，中央宜黄色。"
    }
    
    for keyword, advice in advice_map.items():
        if keyword in question:
            return {"advice": advice, "keyword": keyword}
    
    return {"advice": "请提供更具体的问题，比如关于客厅、卧室、办公室等具体空间的风水咨询。", "keyword": "通用"}

@function_tool
async def get_direction_advice(direction: str):
    """获取方位建议的工具函数"""
    direction_map = {
        "东": "东方属木，宜放置绿色植物，有利于健康和成长。",
        "南": "南方属火，宜用红色装饰，有利于名声和事业。",
        "西": "西方属金，宜用白色或金色，有利于贵人运。",
        "北": "北方属水，宜用蓝色或黑色，有利于事业发展。",
        "东南": "东南方为财位，宜保持明亮整洁，可放置招财植物。",
        "西南": "西南方主感情，宜用粉色或红色装饰。",
        "东北": "东北方为文昌位，宜放置书籍或文昌塔。",
        "西北": "西北方主贵人，宜保持整洁，可放置金属装饰。"
    }
    
    advice = direction_map.get(direction, f"关于{direction}方位，建议根据五行理论进行布置。")
    return {"advice": advice, "direction": direction}

@function_tool
async def get_color_advice(color: str):
    """获取颜色风水建议的工具函数"""
    color_map = {
        "红色": "红色属火，象征热情和活力，适合南方或客厅使用，但不宜过多。",
        "绿色": "绿色属木，象征生机和健康，适合东方或书房使用。",
        "蓝色": "蓝色属水，象征智慧和冷静，适合北方或书房使用。",
        "白色": "白色属金，象征纯洁和简约，适合西方或卧室使用。",
        "黄色": "黄色属土，象征稳定和财富，适合中央位置或餐厅使用。",
        "黑色": "黑色属水，象征深沉和神秘，适合北方，但不宜大面积使用。"
    }
    
    advice = color_map.get(color, f"关于{color}的风水建议，请咨询具体的使用场景。")
    return {"advice": advice, "color": color}

class FengshuiAgent(Agent):
    """风水AI助手 - 基于LiveKit官方标准实现"""
    
    def __init__(self) -> None:
        self._tasks = []  # 防止垃圾回收
        self._latest_frame = None
        self._video_stream = None
        
        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师，具备图片分析能力。

🎯 你的专长：住宅风水、商业风水、方位布局、色彩搭配、图片风水分析

📷 图片分析能力：
当你看到图像时，请详细分析：
- 房间的整体布局和空间感
- 家具的摆放位置和朝向
- 颜色搭配和装饰元素
- 采光和通风情况
- 可能存在的风水问题
- 具体的改善建议

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释风水概念
- 给出具体可行的建议
- 当看到图像时，自然地描述所见并提供分析

💡 回答原则：
- 每次回答控制在200字以内，简洁有力
- 可以使用工具获取详细的专业建议
- 始终保持专业和友善的态度

请用中文与用户交流，提供专业的风水咨询服务。
""",
            tools=[get_fengshui_advice, get_direction_advice, get_color_advice],
        )
    
    async def on_enter(self):
        """Agent进入房间时的初始化 - 基于官方文档实现"""
        print("🏮 风水AI助手进入房间")

        # 1. 注册图片数据处理器 (基于官方Data Messages文档)
        room = get_job_context().room

        @room.on("data_received")
        def on_data_received(data: bytes, participant: rtc.RemoteParticipant, topic: str = None):
            if topic == "images":
                print(f"📷 接收到来自 {participant.identity} 的图片数据")
                task = asyncio.create_task(
                    self._process_image_data(data, participant.identity)
                )
                self._tasks.append(task)
                task.add_done_callback(lambda t: self._tasks.remove(t))

        print("✅ 图片数据处理器已注册 (Data Messages)")
        
        # 2. 设置视频帧处理 (基于官方Vision文档)
        room = get_job_context().room
        
        # 查找现有的视频轨道
        for participant in room.remote_participants.values():
            for publication in participant.track_publications.values():
                if publication.track and publication.track.kind == rtc.TrackKind.KIND_VIDEO:
                    self._create_video_stream(publication.track)
                    break
        
        # 监听新的视频轨道
        @room.on("track_subscribed")
        def on_track_subscribed(track: rtc.Track, publication: rtc.RemoteTrackPublication, participant: rtc.RemoteParticipant):
            if track.kind == rtc.TrackKind.KIND_VIDEO:
                self._create_video_stream(track)
        
        print("✅ 视频帧处理器已设置")
    
    async def _process_image_data(self, image_bytes: bytes, participant_identity: str):
        """处理接收到的图片数据 - 基于官方Data Messages文档"""
        print(f"📷 处理来自 {participant_identity} 的图片，大小: {len(image_bytes)} bytes")
        
        print(f"📷 图片大小: {len(image_bytes)} bytes")
        
        # 将图片添加到聊天上下文
        chat_ctx = self.chat_ctx.copy()
        
        # 编码为base64并添加到聊天上下文
        image_data_url = f"data:image/png;base64,{base64.b64encode(image_bytes).decode('utf-8')}"
        chat_ctx.add_message(
            role="user",
            content=[
                "请分析这张图片的风水布局",
                ImageContent(image=image_data_url)
            ],
        )
        
        await self.update_chat_ctx(chat_ctx)
        print("✅ 图片已添加到聊天上下文")
    
    def _create_video_stream(self, track: rtc.Track):
        """创建视频流处理 - 基于官方Vision文档"""
        # 关闭现有流
        if self._video_stream is not None:
            self._video_stream.close()
        
        # 创建新的视频流
        self._video_stream = rtc.VideoStream(track)
        
        async def read_stream():
            async for event in self._video_stream:
                # 存储最新帧供后续使用
                self._latest_frame = event.frame
        
        # 存储异步任务
        task = asyncio.create_task(read_stream())
        task.add_done_callback(lambda t: self._tasks.remove(t))
        self._tasks.append(task)
        
        print("✅ 视频流已创建")
    
    async def on_user_turn_completed(self, turn_ctx: ChatContext, new_message: ChatMessage) -> None:
        """用户回合完成时 - 添加最新视频帧 (基于官方Vision文档)"""
        # 如果有最新的视频帧，添加到消息中
        if self._latest_frame:
            new_message.content.append(ImageContent(image=self._latest_frame))
            self._latest_frame = None
            print("📷 已添加最新视频帧到对话")

async def entrypoint(ctx: JobContext):
    """Agent入口点 - 基于官方标准实现"""
    await ctx.connect()
    print("🏮 启动风水AI助手...")
    print(f"🎯 Agent会话开始: {ctx.room.name}")

    # 初始化所有组件
    stt = deepgram.STT(model="nova-2-general", language="zh", smart_format=True)
    print("✅ Deepgram STT初始化成功")

    tts = cartesia.TTS(model="sonic-2", voice="f786b574-daa5-4673-aa0c-cbe3e8534c02", language="zh")
    print("✅ Cartesia TTS初始化成功")

    vad = silero.VAD.load()
    print("✅ Silero VAD初始化成功")
    
    from livekit.plugins import openai
    deepseek_llm = openai.LLM(
        model="deepseek-chat",
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        base_url="https://api.deepseek.com/v1",
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功")
    
    # 创建Agent会话
    session = AgentSession(stt=stt, llm=deepseek_llm, tts=tts, vad=vad)
    
    # 启动会话
    await session.start(agent=FengshuiAgent(), room=ctx.room)
    
    print("🎯 风水AI助手已就绪，等待用户交互")

if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
