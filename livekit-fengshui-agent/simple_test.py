#!/usr/bin/env python3
"""
简单测试Agent连接
"""

import asyncio
import os
from dotenv import load_dotenv
from livekit import rtc, api

load_dotenv()

async def simple_test():
    """简单测试"""
    print("🧪 开始简单测试...")
    
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    ws_url = os.getenv("LIVEKIT_URL")
    
    print(f"API Key: {api_key[:10] if api_key else 'None'}...")
    print(f"URL: {ws_url}")
    
    if not all([api_key, api_secret, ws_url]):
        print("❌ 配置不完整")
        return
    
    try:
        # 生成token
        token = (
            api.AccessToken(api_key, api_secret)
            .with_identity("test-user")
            .with_name("Test User")
            .with_grants(
                api.VideoGrants(
                    room_join=True,
                    room="test-room",
                    can_publish=True,
                    can_subscribe=True,
                )
            )
            .to_jwt()
        )
        
        print("✅ Token生成成功")
        
        # 连接房间
        room = rtc.Room()
        
        @room.on("participant_connected")
        def on_participant_connected(participant):
            print(f"✅ 参与者连接: {participant.identity}")
        
        await room.connect(ws_url, token)
        print(f"✅ 连接成功: {room.name}")
        
        # 等待10秒
        print("⏳ 等待10秒...")
        await asyncio.sleep(10)
        
        await room.disconnect()
        print("✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    asyncio.run(simple_test())
