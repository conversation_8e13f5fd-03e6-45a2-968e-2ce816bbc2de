"""
LiveKit风水AI助手 - 基于Git备份的稳定配置
完全按照备份版本重建，删除所有OpenAI冲突配置
"""

import asyncio
import json
import os
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
)
import logging
from livekit.plugins import deepgram, cartesia, silero

# 设置日志
logger = logging.getLogger(__name__)

load_dotenv()

# LiveKit官方视频帧处理函数
async def get_video_track(room: rtc.Room):
    """Find and return the first available remote video track in the room."""
    for participant_id, participant in room.remote_participants.items():
        for track_id, track_publication in participant.track_publications.items():
            if track_publication.track and isinstance(
                track_publication.track, rtc.RemoteVideoTrack
            ):
                logger.info(
                    f"Found video track {track_publication.track.sid} "
                    f"from participant {participant_id}"
                )
                return track_publication.track
    raise ValueError("No remote video track found in the room")

async def get_latest_image(room: rtc.Room):
    """Capture and return a single frame from the video track."""
    video_stream = None
    try:
        video_track = await get_video_track(room)
        video_stream = rtc.VideoStream(video_track)
        async for event in video_stream:
            logger.debug("Captured latest video frame")
            return event.frame
    except Exception as e:
        logger.error(f"Failed to get latest image: {e}")
        return None
    finally:
        if video_stream:
            await video_stream.aclose()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 知识库加载失败: {e}")
        return None

# 全局加载知识库
FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()

@function_tool
async def get_fengshui_advice(location: str, room_type: str):
    """获取风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "room_advice" in FENGSHUI_KNOWLEDGE:
        room_data = FENGSHUI_KNOWLEDGE["room_advice"].get(room_type)
        if room_data:
            advice = room_data.get("basic", "")
            detailed = room_data.get("detailed", {})
            for category, content in detailed.items():
                advice += f"\n【{category}】{content}"
        else:
            advice = f"抱歉，暂时没有关于{room_type}的详细建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "location": location, "room_type": room_type}

@function_tool
async def get_direction_advice(direction: str):
    """获取方位风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "direction_advice" in FENGSHUI_KNOWLEDGE:
        direction_data = FENGSHUI_KNOWLEDGE["direction_advice"].get(direction)
        if direction_data:
            advice = f"【{direction}方位风水建议】\n"
            advice += f"五行属性：{direction_data.get('属性', '未知')}\n"
            advice += f"适合布置：{direction_data.get('适合', '无特殊建议')}\n"
            advice += f"推荐颜色：{direction_data.get('颜色', '无特殊要求')}\n"
            advice += f"注意禁忌：{direction_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{direction}方位的详细建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "direction": direction}

@function_tool
async def get_color_advice(color: str):
    """获取颜色风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "color_advice" in FENGSHUI_KNOWLEDGE:
        color_data = FENGSHUI_KNOWLEDGE["color_advice"].get(color)
        if color_data:
            advice = f"【{color}在风水中的寓意】\n"
            advice += f"象征意义：{color_data.get('寓意', '无特殊寓意')}\n"
            advice += f"适用场所：{color_data.get('适用', '无特殊要求')}\n"
            advice += f"使用禁忌：{color_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{color}的详细风水建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "color": color}

class FengshuiAgent(Agent):
    """风水AI助手 - 支持多模态图片分析"""

    def __init__(self) -> None:
        self._tasks = []  # 防止垃圾回收

        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师，具备实时视觉分析能力。

🎯 你的专长：住宅风水、商业风水、方位布局、色彩搭配、实时视觉分析

📷 视觉分析能力：
当你看到图像时，请自然地将视觉信息融入回答：
- 分析房间布局和家具摆放
- 评估颜色搭配和装饰元素
- 识别风水问题并提供改善建议
- 结合传统风水理论给出专业意见

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释风水概念
- 给出具体可行的建议
- 当看到图像时，自然地描述所见并提供分析

💡 回答原则：
- 每次回答控制在200字以内，简洁有力
- 可以使用工具获取详细的专业建议
- 始终保持专业和友善的态度
- 保持视觉描述简洁但信息丰富

请用中文与用户交流，提供专业的风水咨询服务。当用户开启摄像头时，你可以实时看到他们的环境并提供相应的风水建议。
""",
            tools=[get_fengshui_advice, get_direction_advice, get_color_advice],
        )

    async def on_enter(self):
        """Agent进入房间时的初始化"""
        from livekit.agents import get_job_context
        room = get_job_context().room

        # 1. 注册数据消息处理器 (支持publishData方法)
        @room.on("data_received")
        def on_data_received(data: bytes, participant, topic: str = None):
            if topic == "images":
                print(f"📷 接收到来自 {participant.identity} 的图片数据 (Data Messages)")
                task = asyncio.create_task(
                    self._process_image_data(data, participant.identity)
                )
                self._tasks.append(task)
                task.add_done_callback(lambda t: self._tasks.remove(t))

        # 2. 注册字节流处理器 (支持sendFile方法) - 保持兼容性
        async def _image_received_handler(reader, participant_identity):
            """异步图片处理器 - 符合LiveKit官方规范"""
            await self._image_received(reader, participant_identity)

        room.register_byte_stream_handler("images", _image_received_handler)
        print("✅ 图片处理器已注册 (支持Data Messages + Byte Streams)")

    async def _process_image_data(self, image_bytes: bytes, participant_identity: str):
        """处理publishData发送的图片数据"""
        print(f"📷 处理来自 {participant_identity} 的图片，大小: {len(image_bytes)} bytes")

        # 调用现有的图片分析方法
        await self._analyze_image_with_deepseek(image_bytes, "请分析这张图片的风水布局")

    async def _image_received(self, reader, participant_identity):
        """处理接收到的图片 - 使用DeepSeek Vision API"""
        print(f"📷 接收到来自 {participant_identity} 的图片")

        image_bytes = bytes()
        async for chunk in reader:
            image_bytes += chunk

        print(f"📊 图片大小: {len(image_bytes)} bytes")

        # 使用DeepSeek Vision API分析图片
        try:
            analysis_result = await self._analyze_image_with_deepseek(image_bytes)

            # 通过Agent的say方法回答
            await self._speak_analysis_result(analysis_result)
            print("✅ 图片分析完成并已语音回答")

        except Exception as e:
            print(f"❌ 图片处理失败: {e}")
            await self._speak_analysis_result(self._fallback_image_analysis())

    async def _analyze_image_with_deepseek(self, image_bytes, user_description=""):
        """实用的图片分析方案：用户描述 + DeepSeek文本分析"""
        try:
            import requests
            from PIL import Image
            import io

            # 如果用户没有提供描述，生成基础图片信息
            if not user_description:
                user_description = self._generate_basic_description(image_bytes)

            headers = {
                'Authorization': f'Bearer {os.getenv("DEEPSEEK_API_KEY")}',
                'Content-Type': 'application/json'
            }

            prompt = f"""请作为专业风水大师分析以下房间情况：

房间描述：{user_description}

请提供：
1. 🏠 布局分析：评价当前布局的风水优缺点
2. ⚠️ 问题识别：指出可能的风水问题
3. 💡 改善建议：提供3-5个具体的改善方案
4. 🎨 装饰建议：推荐合适的颜色和装饰

请用专业但通俗易懂的语言，控制在200字内。"""

            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 500
            }

            response = requests.post(
                'https://api.deepseek.com/v1/chat/completions',
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                analysis = result['choices'][0]['message']['content']
                print(f"✅ DeepSeek文本分析成功")
                return analysis
            else:
                print(f"❌ DeepSeek API错误: {response.status_code}")
                return self._fallback_image_analysis()

        except Exception as e:
            print(f"❌ DeepSeek分析失败: {e}")
            return self._fallback_image_analysis()

    def _generate_basic_description(self, image_bytes):
        """生成基础图片描述"""
        try:
            from PIL import Image
            import io

            image = Image.open(io.BytesIO(image_bytes))
            width, height = image.size

            # 分析主要颜色
            colors = image.getcolors(maxcolors=256*256*256)
            if colors:
                dominant_color = max(colors, key=lambda x: x[0])
                color_rgb = dominant_color[1]

                if sum(color_rgb) > 600:
                    color_desc = "整体偏亮色调"
                elif sum(color_rgb) < 300:
                    color_desc = "整体偏暗色调"
                else:
                    color_desc = "色调适中"
            else:
                color_desc = "色调复杂"

            # 基于尺寸推测房间类型
            if width > height:
                layout_type = "横向布局的房间，可能是客厅或办公室"
            else:
                layout_type = "纵向布局的房间，可能是卧室或书房"

            description = f"这是一个{layout_type}，{color_desc}，图片尺寸{width}x{height}像素。请基于一般的室内风水原理进行分析。"

            return description

        except Exception as e:
            return "一个普通的室内空间，请提供通用的风水建议。"

    def _fallback_image_analysis(self):
        """降级图片分析 - 基于传统风水理论"""
        return """根据传统风水理论，我为您提供以下建议：
        1. 确保房间采光充足，避免阴暗角落
        2. 家具摆放要整齐有序，避免杂乱
        3. 选择温暖的色调，如米色、浅黄色
        4. 保持空气流通，避免死角积气
        5. 可以添加一些绿植来改善气场
        如果您能详细描述房间情况，我可以提供更精确的建议。"""

    async def _speak_analysis_result(self, analysis_text):
        """通过语音播报分析结果"""
        try:
            # 这里需要获取当前的session来调用say方法
            # 由于架构限制，我们先打印结果，后续可以通过事件机制传递
            print(f"🗣️ 准备语音播报: {analysis_text}")

            # TODO: 实现语音播报功能
            # 可以通过全局session或事件机制来实现

        except Exception as e:
            print(f"❌ 语音播报失败: {e}")





async def entrypoint(ctx: JobContext):
    """Agent入口点 - 基础风水咨询"""
    await ctx.connect()
    print("🏮 启动风水AI助手...")
    print(f"🎯 Agent会话开始: {ctx.room.name}")

    # 初始化语音识别 (Deepgram)
    stt = deepgram.STT(
        model="nova-2-general",
        language="zh",
        smart_format=True,
    )
    print("✅ Deepgram STT初始化成功")
    
    # 初始化语音合成 (使用Cartesia TTS - 中文语音合成)
    tts = cartesia.TTS(
        model="sonic-2",
        voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 中文女声
        language="zh",
    )
    print("✅ Cartesia TTS初始化成功")
    
    # 初始化语音活动检测 (Silero VAD)
    vad = silero.VAD.load()
    print("✅ Silero VAD初始化成功")
    
    # 使用DeepSeek LLM - 按照正确备份配置
    from livekit.plugins import openai

    deepseek_llm = openai.LLM.with_deepseek(
        model="deepseek-chat",
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功")
    
    # 创建Agent会话
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,
        tts=tts,
        vad=vad,
    )
    
    # 启动会话
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )
    
    print("🎯 风水AI助手已就绪，等待用户交互")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # 使用自动分发模式，不设置agent_name
            # 这样任何用户连接都会触发entrypoint
        )
    )
