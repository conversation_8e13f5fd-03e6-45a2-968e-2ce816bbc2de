"""
LiveKit风水AI助手 - 基于Git备份的稳定配置
完全按照备份版本重建，删除所有OpenAI冲突配置
"""

import asyncio
import json
import os
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
    AutoSubscribe,
)
from livekit import agents, rtc
from livekit.agents.llm import ChatMessage, ImageContent
import base64
import asyncio
import logging
from livekit.plugins import deepgram, cartesia, silero

# 设置日志
logger = logging.getLogger(__name__)

load_dotenv()

# LiveKit官方视频帧处理函数
async def get_video_track(room: rtc.Room):
    """Find and return the first available remote video track in the room."""
    for participant_id, participant in room.remote_participants.items():
        for track_id, track_publication in participant.track_publications.items():
            if track_publication.track and isinstance(
                track_publication.track, rtc.RemoteVideoTrack
            ):
                logger.info(
                    f"Found video track {track_publication.track.sid} "
                    f"from participant {participant_id}"
                )
                return track_publication.track
    raise ValueError("No remote video track found in the room")

async def get_latest_image(room: rtc.Room):
    """Capture and return a single frame from the video track."""
    video_stream = None
    try:
        video_track = await get_video_track(room)
        video_stream = rtc.VideoStream(video_track)
        async for event in video_stream:
            logger.debug("Captured latest video frame")
            return event.frame
    except Exception as e:
        logger.error(f"Failed to get latest image: {e}")
        return None
    finally:
        if video_stream:
            await video_stream.aclose()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 知识库加载失败: {e}")
        return None

# 全局加载知识库
FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()

@function_tool
async def get_fengshui_advice(location: str, room_type: str):
    """获取风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "room_advice" in FENGSHUI_KNOWLEDGE:
        room_data = FENGSHUI_KNOWLEDGE["room_advice"].get(room_type)
        if room_data:
            advice = room_data.get("basic", "")
            detailed = room_data.get("detailed", {})
            for category, content in detailed.items():
                advice += f"\n【{category}】{content}"
        else:
            advice = f"抱歉，暂时没有关于{room_type}的详细建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "location": location, "room_type": room_type}

@function_tool
async def get_direction_advice(direction: str):
    """获取方位风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "direction_advice" in FENGSHUI_KNOWLEDGE:
        direction_data = FENGSHUI_KNOWLEDGE["direction_advice"].get(direction)
        if direction_data:
            advice = f"【{direction}方位风水建议】\n"
            advice += f"五行属性：{direction_data.get('属性', '未知')}\n"
            advice += f"适合布置：{direction_data.get('适合', '无特殊建议')}\n"
            advice += f"推荐颜色：{direction_data.get('颜色', '无特殊要求')}\n"
            advice += f"注意禁忌：{direction_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{direction}方位的详细建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "direction": direction}

@function_tool
async def get_color_advice(color: str):
    """获取颜色风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "color_advice" in FENGSHUI_KNOWLEDGE:
        color_data = FENGSHUI_KNOWLEDGE["color_advice"].get(color)
        if color_data:
            advice = f"【{color}在风水中的寓意】\n"
            advice += f"象征意义：{color_data.get('寓意', '无特殊寓意')}\n"
            advice += f"适用场所：{color_data.get('适用', '无特殊要求')}\n"
            advice += f"使用禁忌：{color_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{color}的详细风水建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "color": color}

class FengshuiAgent(Agent):
    """风水AI助手 - 支持多模态图片分析"""

    def __init__(self) -> None:
        self._tasks = []  # 防止垃圾回收

        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师，具备实时视觉分析能力。

🎯 你的专长：住宅风水、商业风水、方位布局、色彩搭配、实时视觉分析

📷 视觉分析能力：
当你看到图像时，请自然地将视觉信息融入回答：
- 分析房间布局和家具摆放
- 评估颜色搭配和装饰元素
- 识别风水问题并提供改善建议
- 结合传统风水理论给出专业意见

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释风水概念
- 给出具体可行的建议
- 当看到图像时，自然地描述所见并提供分析

💡 回答原则：
- 每次回答控制在200字以内，简洁有力
- 可以使用工具获取详细的专业建议
- 始终保持专业和友善的态度
- 保持视觉描述简洁但信息丰富

请用中文与用户交流，提供专业的风水咨询服务。当用户开启摄像头时，你可以实时看到他们的环境并提供相应的风水建议。
""",
            tools=[get_fengshui_advice, get_direction_advice, get_color_advice, self.search_knowledge_images, self.search_web_images, self.analyze_server_screenshot],
        )

    async def on_enter(self):
        """Agent进入房间时的初始化"""
        def _image_received_handler(reader, participant_identity):
            task = asyncio.create_task(
                self._image_received(reader, participant_identity)
            )
            self._tasks.append(task)
            task.add_done_callback(lambda t: self._tasks.remove(t))

        # 注册图片接收处理器
        from livekit.agents import get_job_context
        get_job_context().room.register_byte_stream_handler("images", _image_received_handler)
        print("✅ 图片上传处理器已注册")

    async def _image_received(self, reader, participant_identity):
        """处理接收到的图片 - 使用DeepSeek Vision API"""
        print(f"📷 接收到来自 {participant_identity} 的图片")

        image_bytes = bytes()
        async for chunk in reader:
            image_bytes += chunk

        print(f"📊 图片大小: {len(image_bytes)} bytes")

        # 使用DeepSeek Vision API分析图片
        try:
            analysis_result = await self._analyze_image_with_deepseek(image_bytes)

            # 通过Agent的say方法回答
            await self._speak_analysis_result(analysis_result)
            print("✅ 图片分析完成并已语音回答")

        except Exception as e:
            print(f"❌ 图片处理失败: {e}")
            await self._speak_analysis_result(self._fallback_image_analysis())

    async def _analyze_image_with_deepseek(self, image_bytes, user_description=""):
        """实用的图片分析方案：用户描述 + DeepSeek文本分析"""
        try:
            import requests
            from PIL import Image
            import io

            # 如果用户没有提供描述，生成基础图片信息
            if not user_description:
                user_description = self._generate_basic_description(image_bytes)

            headers = {
                'Authorization': f'Bearer {os.getenv("DEEPSEEK_API_KEY")}',
                'Content-Type': 'application/json'
            }

            prompt = f"""请作为专业风水大师分析以下房间情况：

房间描述：{user_description}

请提供：
1. 🏠 布局分析：评价当前布局的风水优缺点
2. ⚠️ 问题识别：指出可能的风水问题
3. 💡 改善建议：提供3-5个具体的改善方案
4. 🎨 装饰建议：推荐合适的颜色和装饰

请用专业但通俗易懂的语言，控制在200字内。"""

            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 500
            }

            response = requests.post(
                'https://api.deepseek.com/v1/chat/completions',
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                analysis = result['choices'][0]['message']['content']
                print(f"✅ DeepSeek文本分析成功")
                return analysis
            else:
                print(f"❌ DeepSeek API错误: {response.status_code}")
                return self._fallback_image_analysis()

        except Exception as e:
            print(f"❌ DeepSeek分析失败: {e}")
            return self._fallback_image_analysis()

    def _generate_basic_description(self, image_bytes):
        """生成基础图片描述"""
        try:
            from PIL import Image
            import io

            image = Image.open(io.BytesIO(image_bytes))
            width, height = image.size

            # 分析主要颜色
            colors = image.getcolors(maxcolors=256*256*256)
            if colors:
                dominant_color = max(colors, key=lambda x: x[0])
                color_rgb = dominant_color[1]

                if sum(color_rgb) > 600:
                    color_desc = "整体偏亮色调"
                elif sum(color_rgb) < 300:
                    color_desc = "整体偏暗色调"
                else:
                    color_desc = "色调适中"
            else:
                color_desc = "色调复杂"

            # 基于尺寸推测房间类型
            if width > height:
                layout_type = "横向布局的房间，可能是客厅或办公室"
            else:
                layout_type = "纵向布局的房间，可能是卧室或书房"

            description = f"这是一个{layout_type}，{color_desc}，图片尺寸{width}x{height}像素。请基于一般的室内风水原理进行分析。"

            return description

        except Exception as e:
            return "一个普通的室内空间，请提供通用的风水建议。"

    def _fallback_image_analysis(self):
        """降级图片分析 - 基于传统风水理论"""
        return """根据传统风水理论，我为您提供以下建议：
        1. 确保房间采光充足，避免阴暗角落
        2. 家具摆放要整齐有序，避免杂乱
        3. 选择温暖的色调，如米色、浅黄色
        4. 保持空气流通，避免死角积气
        5. 可以添加一些绿植来改善气场
        如果您能详细描述房间情况，我可以提供更精确的建议。"""

    async def _speak_analysis_result(self, analysis_text):
        """通过语音播报分析结果"""
        try:
            # 这里需要获取当前的session来调用say方法
            # 由于架构限制，我们先打印结果，后续可以通过事件机制传递
            print(f"🗣️ 准备语音播报: {analysis_text}")

            # TODO: 实现语音播报功能
            # 可以通过全局session或事件机制来实现

        except Exception as e:
            print(f"❌ 语音播报失败: {e}")

    @function_tool()
    async def search_knowledge_images(self, query: str) -> str:
        """搜索知识库中的参考图片

        Args:
            query: 搜索关键词，如"客厅"、"办公室"等
        """
        try:
            # 创建图片存储目录
            images_dir = os.path.join(os.path.dirname(__file__), 'knowledge_images')
            if not os.path.exists(images_dir):
                os.makedirs(images_dir)
                print(f"✅ 创建图片目录: {images_dir}")

            # 搜索相关图片文件
            image_files = []
            if os.path.exists(images_dir):
                for filename in os.listdir(images_dir):
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                        # 简单的文件名匹配
                        if query.lower() in filename.lower():
                            image_files.append(filename)

            if image_files:
                result = f"找到{len(image_files)}张关于'{query}'的参考图片：\n"
                for i, filename in enumerate(image_files[:3], 1):
                    result += f"{i}. {filename}\n"
                    # 分析参考图片
                    analysis = await self._analyze_knowledge_image(os.path.join(images_dir, filename))
                    result += f"   分析：{analysis[:50]}...\n"
                return result
            else:
                return f"知识库中暂无关于'{query}'的参考图片，建议您上传相关图片进行分析。"

        except Exception as e:
            return f"图片搜索功能遇到问题：{e}，请直接上传图片进行分析。"

    async def _analyze_knowledge_image(self, image_path: str):
        """分析知识库中的参考图片"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    image_bytes = f.read()

                # 使用DeepSeek分析参考图片
                analysis = await self._analyze_image_with_deepseek(image_bytes)
                print(f"📋 参考图片分析: {os.path.basename(image_path)}")
                return analysis
            else:
                return "参考图片文件不存在"

        except Exception as e:
            print(f"❌ 参考图片分析失败: {e}")
            return "参考图片分析失败"

    @function_tool()
    async def search_web_images(self, query: str) -> str:
        """搜索网络图片进行风水分析

        Args:
            query: 搜索关键词，如"现代客厅"、"中式办公室"等
        """
        try:
            import requests
            from urllib.parse import quote

            # 使用免费的图片搜索API
            search_url = "https://api.unsplash.com/search/photos"
            params = {
                'query': f"{query} interior design",
                'per_page': 2,
                'client_id': os.getenv('UNSPLASH_ACCESS_KEY', 'demo')
            }

            response = requests.get(search_url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                images = data.get('results', [])

                if images:
                    result = f"找到{len(images)}张关于'{query}'的网络参考图片：\n"
                    for i, img in enumerate(images, 1):
                        result += f"{i}. {img.get('description', '室内设计图片')}\n"
                        # 分析网络图片
                        analysis = await self._analyze_web_image(img['urls']['regular'])
                        result += f"   风水分析：{analysis[:50]}...\n"
                    return result
                else:
                    return f"未找到关于'{query}'的参考图片，建议您直接上传图片进行分析。"
            else:
                return "网络图片搜索服务暂时不可用，请直接上传您的图片。"

        except Exception as e:
            print(f"❌ 网络图片搜索失败: {e}")
            return "网络图片搜索功能暂时不可用，请直接描述您的问题或上传图片。"

    async def _analyze_web_image(self, image_url: str):
        """分析网络图片"""
        try:
            import requests

            # 下载图片
            response = requests.get(image_url, timeout=10)
            if response.status_code == 200:
                image_bytes = response.content
                # 使用DeepSeek分析
                analysis = await self._analyze_image_with_deepseek(image_bytes)
                return analysis
            else:
                return "网络图片下载失败"

        except Exception as e:
            print(f"❌ 网络图片分析失败: {e}")
            return "网络图片分析失败"

    @function_tool()
    async def analyze_server_screenshot(self, description: str = "最新截图") -> str:
        """分析服务器保存的截图

        Args:
            description: 截图描述，用于定位文件
        """
        try:
            # 查找服务器上的截图文件
            screenshot_dir = "/www/wwwroot/su.guiyunai.fun/screenshots"

            # 如果目录不存在，创建它
            if not os.path.exists(screenshot_dir):
                os.makedirs(screenshot_dir)
                # 创建一个示例截图说明文件
                readme_path = os.path.join(screenshot_dir, "README.txt")
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write("这里存放需要分析的截图文件\n支持格式：PNG, JPG, JPEG\n")
                return f"已创建截图目录 {screenshot_dir}，请将需要分析的图片放入此目录。"

            # 查找图片文件
            image_files = []
            for filename in os.listdir(screenshot_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    file_path = os.path.join(screenshot_dir, filename)
                    image_files.append((filename, os.path.getctime(file_path)))

            if image_files:
                # 按创建时间排序，获取最新的截图
                image_files.sort(key=lambda x: x[1], reverse=True)
                latest_file = image_files[0][0]
                file_path = os.path.join(screenshot_dir, latest_file)

                # 读取图片并分析
                with open(file_path, 'rb') as f:
                    image_bytes = f.read()

                print(f"📷 正在分析服务器截图: {latest_file}")
                analysis = await self._analyze_image_with_deepseek(image_bytes)

                result = f"✅ 已分析服务器截图 {latest_file}：\n\n{analysis}"
                return result
            else:
                return f"服务器截图目录 {screenshot_dir} 中暂无图片文件，请上传图片后重试。"

        except Exception as e:
            print(f"❌ 服务器截图分析失败: {e}")
            return f"服务器截图功能遇到问题：{e}，请尝试直接上传图片。"

async def entrypoint(ctx: JobContext):
    """Agent入口点 - 支持视频分析"""
    # 启用视频订阅以支持图片分析
    await ctx.connect(auto_subscribe=AutoSubscribe.SUBSCRIBE_ALL)
    print("🏮 启动风水AI助手 (支持图片分析)...")
    print(f"🎯 Agent会话开始: {ctx.room.name}")

    # LLM回调函数 - 在生成回复前注入最新视频帧
    async def before_llm_cb(assistant, chat_ctx):
        """
        Callback that runs right before the LLM generates a response.
        Captures the current video frame and adds it to the conversation context.
        """
        try:
            latest_image = await get_latest_image(ctx.room)
            if latest_image:
                image_content = [ImageContent(image=latest_image)]
                chat_ctx.messages.append(ChatMessage(role="user", content=image_content))
                logger.debug("Added latest frame to conversation context")
                print("📷 已捕获视频帧并添加到对话上下文")
        except Exception as e:
            logger.error(f"Failed to capture video frame: {e}")
            print(f"⚠️ 视频帧捕获失败: {e}")

    # 初始化语音识别 (Deepgram)
    stt = deepgram.STT(
        model="nova-2-general",
        language="zh",
        smart_format=True,
    )
    print("✅ Deepgram STT初始化成功")
    
    # 初始化语音合成 (使用Cartesia TTS - 中文语音合成，启用流式优化)
    tts = cartesia.TTS(
        model="sonic-2",
        voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 中文女声
        language="zh",
        # 流式优化配置
        streaming=True,  # 启用流式合成
        word_timestamps=True,  # 启用词级时间戳，用于更精确的中断
    )
    print("✅ Cartesia TTS初始化成功 (流式模式)")
    
    # 初始化语音活动检测 (Silero VAD) - 优化延迟配置
    vad = silero.VAD.load(
        # 优化VAD参数以减少延迟
        min_speech_duration_ms=100,  # 减少最小语音持续时间 (默认250ms)
        min_silence_duration_ms=300,  # 减少最小静音持续时间 (默认500ms)
        speech_pad_ms=50,  # 减少语音填充时间 (默认100ms)
    )
    print("✅ Silero VAD初始化成功 (低延迟模式)")
    
    # 使用DeepSeek LLM - 正确的配置方式
    from livekit.plugins import openai

    deepseek_llm = openai.LLM(
        model="deepseek-chat",
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        base_url="https://api.deepseek.com/v1",
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功")
    
    # 创建Agent会话 - 启用预连接缓冲和延迟优化
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,
        tts=tts,
        vad=vad,
        # 延迟优化配置
        allow_interruptions=True,  # 允许中断，减少感知延迟
        min_endpointing_delay=0.2,  # 减少端点检测延迟 (默认0.4s)
        max_endpointing_delay=4.0,  # 减少最大等待时间 (默认6.0s)
        min_interruption_duration=0.3,  # 减少中断检测时间 (默认0.5s)
        # 抢先式生成 - 基于部分转录开始响应
        preemptive_generation=True,  # 启用抢先式生成
    )
    
    # 启动会话
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )
    
    print("🎯 风水AI助手已就绪，等待用户交互")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # 使用自动分发模式，不设置agent_name
            # 这样任何用户连接都会触发entrypoint
        )
    )
