"""
LiveKit风水AI助手 - 基于Git备份的稳定配置
完全按照备份版本重建，删除所有OpenAI冲突配置
"""

import asyncio
import json
import os
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
)
from livekit import agents, rtc
import base64
import asyncio
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 知识库加载失败: {e}")
        return None

# 全局加载知识库
FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()

@function_tool
async def get_fengshui_advice(location: str, room_type: str):
    """获取风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "room_advice" in FENGSHUI_KNOWLEDGE:
        room_data = FENGSHUI_KNOWLEDGE["room_advice"].get(room_type)
        if room_data:
            advice = room_data.get("basic", "")
            detailed = room_data.get("detailed", {})
            for category, content in detailed.items():
                advice += f"\n【{category}】{content}"
        else:
            advice = f"抱歉，暂时没有关于{room_type}的详细建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "location": location, "room_type": room_type}

@function_tool
async def get_direction_advice(direction: str):
    """获取方位风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "direction_advice" in FENGSHUI_KNOWLEDGE:
        direction_data = FENGSHUI_KNOWLEDGE["direction_advice"].get(direction)
        if direction_data:
            advice = f"【{direction}方位风水建议】\n"
            advice += f"五行属性：{direction_data.get('属性', '未知')}\n"
            advice += f"适合布置：{direction_data.get('适合', '无特殊建议')}\n"
            advice += f"推荐颜色：{direction_data.get('颜色', '无特殊要求')}\n"
            advice += f"注意禁忌：{direction_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{direction}方位的详细建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "direction": direction}

@function_tool
async def get_color_advice(color: str):
    """获取颜色风水建议的工具函数"""
    if FENGSHUI_KNOWLEDGE and "color_advice" in FENGSHUI_KNOWLEDGE:
        color_data = FENGSHUI_KNOWLEDGE["color_advice"].get(color)
        if color_data:
            advice = f"【{color}在风水中的寓意】\n"
            advice += f"象征意义：{color_data.get('寓意', '无特殊寓意')}\n"
            advice += f"适用场所：{color_data.get('适用', '无特殊要求')}\n"
            advice += f"使用禁忌：{color_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{color}的详细风水建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"
    
    return {"advice": advice, "color": color}

class FengshuiAgent(Agent):
    """风水AI助手 - 支持多模态图片分析"""

    def __init__(self) -> None:
        self._tasks = []  # 防止垃圾回收

        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师。

🎯 你的专长：住宅风水、商业风水、方位布局、色彩搭配、图片风水分析

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释风水概念
- 给出具体可行的建议

💡 回答原则：
- 每次回答控制在200字以内，简洁有力
- 可以使用工具获取详细的专业建议
- 始终保持专业和友善的态度
- 当用户上传图片时，详细分析图片中的风水布局

📷 图片分析能力：
- 分析房间布局和家具摆放
- 评估颜色搭配和装饰元素
- 识别风水问题并提供改善建议
- 结合传统风水理论给出专业意见

请用中文与用户交流，提供专业的风水咨询服务。
""",
            tools=[get_fengshui_advice, get_direction_advice, get_color_advice],
        )

    async def on_enter(self):
        """Agent进入房间时的初始化"""
        def _image_received_handler(reader, participant_identity):
            task = asyncio.create_task(
                self._image_received(reader, participant_identity)
            )
            self._tasks.append(task)
            task.add_done_callback(lambda t: self._tasks.remove(t))

        # 注册图片接收处理器
        from livekit.agents import get_job_context
        get_job_context().room.register_byte_stream_handler("images", _image_received_handler)
        print("✅ 图片上传处理器已注册")

    async def _image_received(self, reader, participant_identity):
        """处理接收到的图片"""
        print(f"📷 接收到来自 {participant_identity} 的图片")

        image_bytes = bytes()
        async for chunk in reader:
            image_bytes += chunk

        # 将图片添加到聊天上下文
        try:
            from livekit.agents.llm import ImageContent

            chat_ctx = self.chat_ctx.copy()
            chat_ctx.add_message(
                role="user",
                content=[
                    "请分析这张图片的风水布局，并提供专业建议：",
                    ImageContent(
                        image=f"data:image/png;base64,{base64.b64encode(image_bytes).decode('utf-8')}"
                    )
                ],
            )

            await self.update_chat_ctx(chat_ctx)
            print("✅ 图片已添加到聊天上下文，准备分析")

        except Exception as e:
            print(f"❌ 图片处理失败: {e}")
            # 如果图片处理失败，仍然可以提供文字建议
            print("⚠️ 图片处理功能暂时不可用，但仍可提供语音风水咨询")

async def entrypoint(ctx: JobContext):
    """Agent入口点 - 简化版本"""
    await ctx.connect()  # 关键：必须先连接到房间
    print("🏮 启动风水AI助手...")
    print(f"🎯 Agent会话开始: {ctx.room.name}")

    # 初始化语音识别 (Deepgram)
    stt = deepgram.STT(
        model="nova-2-general",
        language="zh",
        smart_format=True,
    )
    print("✅ Deepgram STT初始化成功")
    
    # 初始化语音合成 (使用Cartesia TTS - 中文语音合成，启用流式优化)
    tts = cartesia.TTS(
        model="sonic-2",
        voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 中文女声
        language="zh",
        # 流式优化配置
        streaming=True,  # 启用流式合成
        word_timestamps=True,  # 启用词级时间戳，用于更精确的中断
    )
    print("✅ Cartesia TTS初始化成功 (流式模式)")
    
    # 初始化语音活动检测 (Silero VAD) - 优化延迟配置
    vad = silero.VAD.load(
        # 优化VAD参数以减少延迟
        min_speech_duration_ms=100,  # 减少最小语音持续时间 (默认250ms)
        min_silence_duration_ms=300,  # 减少最小静音持续时间 (默认500ms)
        speech_pad_ms=50,  # 减少语音填充时间 (默认100ms)
    )
    print("✅ Silero VAD初始化成功 (低延迟模式)")
    
    # 使用DeepSeek LLM - 按照备份版本的方式导入
    from livekit.plugins import openai

    deepseek_llm = openai.LLM.with_deepseek(
        model="deepseek-chat",
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功")
    
    # 创建Agent会话 - 启用预连接缓冲和延迟优化
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,
        tts=tts,
        vad=vad,
        # 延迟优化配置
        allow_interruptions=True,  # 允许中断，减少感知延迟
        min_endpointing_delay=0.2,  # 减少端点检测延迟 (默认0.4s)
        max_endpointing_delay=4.0,  # 减少最大等待时间 (默认6.0s)
        min_interruption_duration=0.3,  # 减少中断检测时间 (默认0.5s)
        # 抢先式生成 - 基于部分转录开始响应
        preemptive_generation=True,  # 启用抢先式生成
    )
    
    # 启动会话
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )
    
    print("🎯 风水AI助手已就绪，等待用户交互")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # 使用自动分发模式，不设置agent_name
            # 这样任何用户连接都会触发entrypoint
        )
    )
