#!/usr/bin/env python3
"""
简单的Token生成服务器
用于测试图片上传功能
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
from livekit import api
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# LiveKit配置
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')
LIVEKIT_URL = os.getenv('LIVEKIT_URL', 'wss://kjh-a5mlk6sq.livekit.cloud')

@app.route('/')
def index():
    """提供测试页面"""
    return send_from_directory('.', 'test_image_upload.html')

@app.route('/api/token', methods=['POST'])
def generate_token():
    """生成LiveKit访问token"""
    try:
        data = request.get_json()
        room_name = data.get('room', 'fengshui-test-room')
        identity = data.get('identity', 'user')
        
        # 创建token
        token = api.AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET) \
            .with_identity(identity) \
            .with_name(identity) \
            .with_grants(api.VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=True,
                can_subscribe=True,
            )).to_jwt()
        
        return jsonify({
            'token': token,
            'url': LIVEKIT_URL,
            'room': room_name,
            'identity': identity
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health():
    """健康检查"""
    return jsonify({'status': 'ok'})

if __name__ == '__main__':
    print("🚀 启动Token服务器...")
    print(f"📍 访问地址: http://localhost:5000")
    print(f"🔗 LiveKit URL: {LIVEKIT_URL}")
    app.run(host='0.0.0.0', port=5000, debug=True)
