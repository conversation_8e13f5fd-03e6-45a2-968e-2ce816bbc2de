#!/bin/bash

# LiveKit风水AI助手 - 生产环境停止脚本

echo "🛑 停止LiveKit风水AI助手服务"
echo "================================"

# 停止前端服务
if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "🛑 停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        echo "✅ 前端服务已停止"
    else
        echo "⚠️  前端服务已经停止"
    fi
    rm -f logs/frontend.pid
fi

# 停止后端Agent
if [ -f "logs/agent.pid" ]; then
    AGENT_PID=$(cat logs/agent.pid)
    if kill -0 $AGENT_PID 2>/dev/null; then
        echo "🛑 停止后端Agent (PID: $AGENT_PID)..."
        kill $AGENT_PID
        echo "✅ 后端Agent已停止"
    else
        echo "⚠️  后端Agent已经停止"
    fi
    rm -f logs/agent.pid
fi

# 强制停止可能残留的进程
pkill -f "next.*7000" 2>/dev/null || true
pkill -f "python.*agent.py" 2>/dev/null || true

echo ""
echo "✅ 所有服务已停止"
