#!/bin/bash

# 历史教育AI助手 - 停止脚本

echo "🛑 停止历史教育AI博物馆服务..."

# 创建日志目录（如果不存在）
mkdir -p logs

# 停止前端服务
if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        rm logs/frontend.pid
    fi
fi

# 停止Agent服务
if [ -f "logs/agent.pid" ]; then
    AGENT_PID=$(cat logs/agent.pid)
    if kill -0 $AGENT_PID 2>/dev/null; then
        echo "停止AI助手 (PID: $AGENT_PID)..."
        kill $AGENT_PID
        rm logs/agent.pid
    fi
fi

# 强制停止相关进程
pkill -f "historical_education_agent" || true
pkill -f "python.*http.server.*7000" || true

echo "✅ 历史教育AI博物馆服务已停止"
