#!/bin/bash

# 历史教育AI助手 - 启动脚本
# 基于LiveKit标准框架的双语历史教育系统

set -e

echo "🏛️ 启动历史教育AI博物馆"
echo "================================"

# 检查端口7000是否被占用
if lsof -Pi :7000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口7000已被占用，正在停止现有服务..."
    pkill -f "python.*7000" || true
    pkill -f "http.server.*7000" || true
    sleep 2
fi

# 启动前端服务 (简单HTTP服务器)
echo "🎨 启动前端服务 (端口7000)..."
cd historical-education-frontend

# 使用Python内置HTTP服务器
nohup python3 -m http.server 7000 > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端启动
sleep 3

# 检查前端是否启动成功
if curl -s http://localhost:7000 > /dev/null; then
    echo "✅ 前端服务启动成功 (PID: $FRONTEND_PID)"
else
    echo "❌ 前端服务启动失败"
    exit 1
fi

# 返回根目录
cd ..

# 启动后端Agent
echo ""
echo "🤖 启动历史教育AI助手..."

# 激活虚拟环境
source venv/bin/activate

# 创建日志目录
mkdir -p logs

# 启动Agent (后台运行)
echo "🚀 启动历史教育AI Agent..."
nohup python historical_education_agent.py start > logs/agent.log 2>&1 &
AGENT_PID=$!

# 等待Agent启动
sleep 5

echo "✅ 历史教育AI助手启动成功 (PID: $AGENT_PID)"

# 保存进程ID
echo $FRONTEND_PID > logs/frontend.pid
echo $AGENT_PID > logs/agent.pid

# 显示启动结果
echo ""
echo "🎉 历史教育AI博物馆启动完成！"
echo "================================"
echo ""
echo "📊 服务状态:"
echo "前端界面: http://localhost:7000 (PID: $FRONTEND_PID)"
echo "AI助手: 运行中 (PID: $AGENT_PID)"
echo "生产域名: https://su.guiyunai.fun"
echo ""
echo "📋 日志文件:"
echo "前端日志: logs/frontend.log"
echo "AI助手日志: logs/agent.log"
echo ""
echo "🛑 停止服务:"
echo "./stop-historical-education.sh"
echo ""
echo "🎯 使命: 铭记历史，珍惜和平，促进理解"
