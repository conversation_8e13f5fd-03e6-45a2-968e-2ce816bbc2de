{"name": "@livekit/agents", "version": "0.7.9", "description": "LiveKit Agents - Node.js", "main": "dist/index.js", "require": "dist/index.cjs", "types": "dist/index.d.ts", "exports": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./index.d.cts", "default": "./index.cjs"}}, "author": "LiveKit", "type": "module", "repository": "**************:livekit/agents-js.git", "license": "Apache-2.0", "files": ["dist", "src", "README.md"], "scripts": {"build": "tsup --onSuccess \"pnpm build:types\"", "build:types": "tsc --declaration --emitDeclarationOnly && node ../scripts/copyDeclarationOutput.js", "clean": "rm -rf dist", "clean:build": "pnpm clean && pnpm build", "lint": "eslint -f unix \"src/**/*.ts\"", "api:check": "api-extractor run --typescript-compiler-folder ../node_modules/typescript", "api:update": "api-extractor run --local --typescript-compiler-folder ../node_modules/typescript --verbose"}, "devDependencies": {"@livekit/rtc-node": "^0.13.12", "@microsoft/api-extractor": "^7.35.0", "@types/node": "^22.5.5", "@types/ws": "^8.5.10", "tsup": "^8.4.0", "typescript": "^5.0.0"}, "dependencies": {"@livekit/mutex": "^1.1.1", "@livekit/protocol": "^1.29.1", "@livekit/typed-emitter": "^3.0.0", "commander": "^12.0.0", "livekit-server-sdk": "^2.9.2", "pino": "^8.19.0", "pino-pretty": "^11.0.0", "ws": "^8.16.0", "zod": "^3.23.8"}, "peerDependencies": {"@livekit/rtc-node": "^0.13.12"}}