{"$schema": "https://unpkg.com/@changesets/config@2.2.0/schema.json", "changelog": ["@livekit/changesets-changelog-github", {"repo": "livekit/agents-js"}], "commit": false, "ignore": ["livekit-agents-examples"], "linked": [], "access": "public", "baseBranch": "main", "updateInternalDependencies": "patch", "___experimentalUnsafeOptions_WILL_CHANGE_IN_PATCH": {"onlyUpdatePeerDependentsWhenOutOfRange": true}}