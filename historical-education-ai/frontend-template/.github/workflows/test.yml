# SPDX-FileCopyrightText: 2024 LiveKit, Inc.
#
# SPDX-License-Identifier: Apache-2.0

name: Test
on:
  push:
    branches: [next, main]
  pull_request:
    branches: [next, main]

jobs:
  build:
    name: Test
    # Don't run tests for PRs on forks
    if: github.event_name != 'pull_request' || github.event.pull_request.head.repo.fork == false
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true
      - uses: pnpm/action-setup@v4
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: pnpm
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Build
        run: pnpm build
      - name: Check which tests to run
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            agents-or-tests:
              - 'agents/**'
              - 'plugins/test/**' 
              - '.github/workflows/**'
            plugins: 
              - 'plugins/**'
            workflows:
              - '.github/workflows/**'
      - name: Test agents
        if: steps.filter.outputs.agents-or-tests == 'true' || steps.filter.outputs.workflows == 'true' || github.event_name != 'pull_request'
        run: pnpm test agents
      - name: Test all plugins
        if: steps.filter.outputs.agents-or-tests == 'true' || steps.filter.outputs.workflows == 'true' || github.event_name != 'pull_request'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ELEVEN_API_KEY: ${{ secrets.ELEVEN_API_KEY }}
          DEEPGRAM_API_KEY: ${{ secrets.DEEPGRAM_API_KEY }}
          CARTESIA_API_KEY: ${{ secrets.CARTESIA_API_KEY }}
          NEUPHONIC_API_KEY: ${{ secrets.NEUPHONIC_API_KEY }}
          RESEMBLE_API_KEY: ${{ secrets.RESEMBLE_API_KEY }}
          ASSEMBLY_AI_KEY: ${{ secrets.ASSEMBLY_AI_KEY }}
        run: pnpm test plugins
      - name: Test specific plugins
        if: steps.filter.outputs.agents-or-tests == 'false' && steps.filter.outputs.workflows == 'false' && steps.filter.outputs.plugins == 'true' && github.event_name == 'pull_request'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ELEVEN_API_KEY: ${{ secrets.ELEVEN_API_KEY }}
          DEEPGRAM_API_KEY: ${{ secrets.DEEPGRAM_API_KEY }}
          CARTESIA_API_KEY: ${{ secrets.CARTESIA_API_KEY }}
          NEUPHONIC_API_KEY: ${{ secrets.NEUPHONIC_API_KEY }}
          RESEMBLE_API_KEY: ${{ secrets.RESEMBLE_API_KEY }}
          ASSEMBLY_AI_KEY: ${{ secrets.ASSEMBLY_AI_KEY }}
        run: |
          plugins=$(git diff-tree --name-only --no-commit-id -r ${{ github.sha }} | grep '^plugins.*\.ts$' | cut -d/ -f2 | sort -u | tr '\n' ' ')
          read -ra plugins <<< "$plugins"
          for plugin in "${plugins[@]}"; do
            pnpm test $plugin
          done
