{
  "plugins": ["@typescript-eslint/eslint-plugin", "eslint-plugin-tsdoc"],
  "extends": [
    "turbo",
    "prettier",
    "plugin:prettier/recommended",
    "plugin:@typescript-eslint/recommended",
  ],
  "env": {
    "node": true,
  },
  "parserOptions": {
    "ecmaVersion": 2022,
    "ecmaFeatures": {},
  },
  "settings": {},
  "rules": {
    "tsdoc/syntax": "warn",
    "space-before-function-parens": 0,
    "@typescript-eslint/no-unused-vars": "error",
    "import/export": 0,
    "@typescript-eslint/ban-ts-comment": "warn",
    "@typescript-eslint/no-empty-interface": "warn",
    "@typescript-eslint/consistent-type-imports": "warn",
    "@typescript-eslint/no-explicit-any": "warn",
  },
}
