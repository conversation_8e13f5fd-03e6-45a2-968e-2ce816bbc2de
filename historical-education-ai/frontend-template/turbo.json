{"$schema": "https://turborepo.org/schema.json", "globalEnv": ["ASSEMBLY_AI_KEY", "AZURE_API_KEY", "AZURE_OPENAI_API_KEY", "AZURE_OPENAI_DEPLOYMENT", "AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_ENTRA_TOKEN", "CARTESIA_API_KEY", "CEREBRAS_API_KEY", "DEEPGRAM_API_KEY", "DEEPSEEK_API_KEY", "ELEVEN_API_KEY", "FIREWORKS_API_KEY", "GROQ_API_KEY", "LIVEKIT_API_KEY", "LIVEKIT_API_SECRET", "LIVEKIT_URL", "LLAMA_API_KEY", "LOG_LEVEL", "OCTOAI_TOKEN", "OPENAI_API_KEY", "PERPLEXITY_API_KEY", "TELNYX_API_KEY", "TOGETHER_API_KEY", "XAI_API_KEY", "NEUPHONIC_API_KEY", "RESEMBLE_API_KEY"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "clean": {"dependsOn": ["^clean"], "outputs": [""]}, "clean:build": {"dependsOn": ["^clean:build"], "outputs": ["dist/**"]}, "lint": {"outputs": []}, "api:check": {"cache": false, "dependsOn": ["^build"]}, "api:update": {"dependsOn": ["^build"]}}}