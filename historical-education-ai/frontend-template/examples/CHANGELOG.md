# livekit-agents-examples

## null

### Patch Changes

- A few more bugs and updates - [#88](https://github.com/livekit/agents-js/pull/88) ([@bcherry](https://github.com/bcherry))

- Updated dependencies [[`56333dd89486a1a10157f57576447d3bb7cb83c3`](https://github.com/livekit/agents-js/commit/56333dd89486a1a10157f57576447d3bb7cb83c3), [`07b4d4b123955bd850a208471d651810e075f0af`](https://github.com/livekit/agents-js/commit/07b4d4b123955bd850a208471d651810e075f0af)]:
  - @livekit/agents@0.3.2
  - @livekit/agents-plugin-openai@0.3.2

## null

### Patch Changes

- Updated dependencies [[`5ec3db8f645d5e45b673318816e2746c3f6ccb1b`](https://github.com/livekit/agents-js/commit/5ec3db8f645d5e45b673318816e2746c3f6ccb1b), [`d3db7cf19c696f611b5717ff8d510b2f910da712`](https://github.com/livekit/agents-js/commit/d3db7cf19c696f611b5717ff8d510b2f910da712), [`c0cce8a0f71cd8def7052917d8a6479e06178447`](https://github.com/livekit/agents-js/commit/c0cce8a0f71cd8def7052917d8a6479e06178447), [`e748aa4f7be76361c5fcafb03bdb760314b29a9f`](https://github.com/livekit/agents-js/commit/e748aa4f7be76361c5fcafb03bdb760314b29a9f), [`b35952ca243fecb087c898b670f5db0eaa1949bf`](https://github.com/livekit/agents-js/commit/b35952ca243fecb087c898b670f5db0eaa1949bf), [`4edacb8ba7dbbdd060dfedffe3116f1af4739b52`](https://github.com/livekit/agents-js/commit/4edacb8ba7dbbdd060dfedffe3116f1af4739b52)]:
  - @livekit/agents-plugin-openai@0.3.1
  - @livekit/agents@0.3.1

## null

### Minor Changes

- Maximize self-import compatibility - [#69](https://github.com/livekit/agents-js/pull/69) ([@bcherry](https://github.com/bcherry))

- omniassistant overhaul - [#65](https://github.com/livekit/agents-js/pull/65) ([@nbsp](https://github.com/nbsp))

### Patch Changes

- update rtc-node to 0.9.0 - [#73](https://github.com/livekit/agents-js/pull/73) ([@nbsp](https://github.com/nbsp))

- Rename to MultimodalAgent, move to main package - [#74](https://github.com/livekit/agents-js/pull/74) ([@bcherry](https://github.com/bcherry))

- Updated dependencies [[`4e6babac612c20b1a8d9121d39fe57902d22228f`](https://github.com/livekit/agents-js/commit/4e6babac612c20b1a8d9121d39fe57902d22228f), [`9cb2313f06f9d013ca3b08980a7ade1b6b43a04a`](https://github.com/livekit/agents-js/commit/9cb2313f06f9d013ca3b08980a7ade1b6b43a04a), [`08b9a329c05a6a1369de7682f555445f669fea79`](https://github.com/livekit/agents-js/commit/08b9a329c05a6a1369de7682f555445f669fea79), [`d703265a57c4491d7799936117a8a2b8ad527653`](https://github.com/livekit/agents-js/commit/d703265a57c4491d7799936117a8a2b8ad527653), [`5cbd46c715ded05107cd78492d85551c2ce924ae`](https://github.com/livekit/agents-js/commit/5cbd46c715ded05107cd78492d85551c2ce924ae), [`eee688907aafdef8ca2856929b8eb10ba72e8dee`](https://github.com/livekit/agents-js/commit/eee688907aafdef8ca2856929b8eb10ba72e8dee), [`9cb2313f06f9d013ca3b08980a7ade1b6b43a04a`](https://github.com/livekit/agents-js/commit/9cb2313f06f9d013ca3b08980a7ade1b6b43a04a), [`856ebe2294962f64b81c8f635bd762b513b2faac`](https://github.com/livekit/agents-js/commit/856ebe2294962f64b81c8f635bd762b513b2faac), [`c509b62972892ea3945403ef0cd50c2ece3fd4f2`](https://github.com/livekit/agents-js/commit/c509b62972892ea3945403ef0cd50c2ece3fd4f2), [`45cb43f41a5d53a048eef392bb81313ad5e95121`](https://github.com/livekit/agents-js/commit/45cb43f41a5d53a048eef392bb81313ad5e95121), [`eb7e73173c46dbbcee4e728299b8fe05fb8fdc01`](https://github.com/livekit/agents-js/commit/eb7e73173c46dbbcee4e728299b8fe05fb8fdc01)]:
  - @livekit/agents-plugin-openai@0.3.0
  - @livekit/agents@0.3.0

## null

### Minor Changes

- bump underlying dependencies - [`be7160d39ea57239a51fbf6ad2cbea1342cc1889`](https://github.com/livekit/agents-js/commit/be7160d39ea57239a51fbf6ad2cbea1342cc1889) ([@bcherry](https://github.com/bcherry))
  fix load calculation
  report worker status

### Patch Changes

- Fix assistant startup process - [#36](https://github.com/livekit/agents-js/pull/36) ([@bcherry](https://github.com/bcherry))

- Send agent transcript progressively and handle interruptions - [#40](https://github.com/livekit/agents-js/pull/40) ([@bcherry](https://github.com/bcherry))

- Updated dependencies [[`1c8caf04c148dfa57af4e844b6538d97d6be652a`](https://github.com/livekit/agents-js/commit/1c8caf04c148dfa57af4e844b6538d97d6be652a), [`5923b1a796642bec4892f41545ea1be1c6b9fb36`](https://github.com/livekit/agents-js/commit/5923b1a796642bec4892f41545ea1be1c6b9fb36), [`ccff5ce34d071a0fb449da5ce77938e346679b1b`](https://github.com/livekit/agents-js/commit/ccff5ce34d071a0fb449da5ce77938e346679b1b), [`be7160d39ea57239a51fbf6ad2cbea1342cc1889`](https://github.com/livekit/agents-js/commit/be7160d39ea57239a51fbf6ad2cbea1342cc1889), [`5c320c88a04ffd8b7753696d4172a610fbe1bc2b`](https://github.com/livekit/agents-js/commit/5c320c88a04ffd8b7753696d4172a610fbe1bc2b), [`24a4f58a23d4a3aad8620fcccabdab5d2e1152c7`](https://github.com/livekit/agents-js/commit/24a4f58a23d4a3aad8620fcccabdab5d2e1152c7), [`1063d2a25c4a01022948699e673d267d04c1ec05`](https://github.com/livekit/agents-js/commit/1063d2a25c4a01022948699e673d267d04c1ec05), [`36c553a60fef7621b9c4232b5c79555b2f83aad8`](https://github.com/livekit/agents-js/commit/36c553a60fef7621b9c4232b5c79555b2f83aad8), [`7e6bb7fbf661e6c6aa012f6a362b84d542d2c84e`](https://github.com/livekit/agents-js/commit/7e6bb7fbf661e6c6aa012f6a362b84d542d2c84e)]:
  - @livekit/agents@0.2.0
  - @livekit/agents-plugin-openai@0.2.0
