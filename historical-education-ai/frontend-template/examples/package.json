{"private": true, "name": "livekit-agents-examples", "type": "module", "scripts": {"build": "tsc", "clean": "rm -rf dist", "clean:build": "pnpm clean && pnpm build", "lint": "eslint -f unix \"src/**/*.ts\"", "minimal": "pnpm exec tsx src/multimodal_agent.ts"}, "devDependencies": {"@types/node": "^22.5.5", "tsx": "^4.19.2", "typescript": "^5.0.0"}, "dependencies": {"@livekit/agents": "workspace:*", "@livekit/agents-plugin-deepgram": "workspace:*", "@livekit/agents-plugin-elevenlabs": "workspace:*", "@livekit/agents-plugin-openai": "workspace:*", "@livekit/agents-plugin-silero": "workspace:*", "@livekit/agents-plugin-livekit": "workspace:*", "livekit-server-sdk": "^2.9.2", "@livekit/rtc-node": "^0.13.11", "zod": "^3.23.8"}, "version": null}