# @livekit/agents-plugin-elevenlabs

## 0.6.3

### Patch Changes

- fix: avoid masquerading types as mjs - [#425](https://github.com/livekit/agents-js/pull/425) ([@lukasIO](https://github.com/lukasIO))

- Updated dependencies [[`18a67fc7a0e1935fbebad4aa0af59414269e1cb1`](https://github.com/livekit/agents-js/commit/18a67fc7a0e1935fbebad4aa0af59414269e1cb1), [`2ec46bbe068b7ec1fb6338ed5156292b148b7c1b`](https://github.com/livekit/agents-js/commit/2ec46bbe068b7ec1fb6338ed5156292b148b7c1b), [`5306b7a3e3d1e7819fdf07a1f4fc77e32dd91dde`](https://github.com/livekit/agents-js/commit/5306b7a3e3d1e7819fdf07a1f4fc77e32dd91dde), [`3e57767f87a5bc79da7d364927ab149813c81314`](https://github.com/livekit/agents-js/commit/3e57767f87a5bc79da7d364927ab149813c81314)]:
  - @livekit/agents@0.7.7

## 0.6.2

### Patch Changes

- bump to use 0.7.0 - [#316](https://github.com/livekit/agents-js/pull/316) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`724c02bb7a91c27d6c8daf961842fb9f0934770c`](https://github.com/livekit/agents-js/commit/724c02bb7a91c27d6c8daf961842fb9f0934770c), [`7398cffad62b17c79b5fe2f0ca4e99e548560367`](https://github.com/livekit/agents-js/commit/7398cffad62b17c79b5fe2f0ca4e99e548560367), [`6ed0c90d1bab013854c416768b10ef96f3227d68`](https://github.com/livekit/agents-js/commit/6ed0c90d1bab013854c416768b10ef96f3227d68), [`33c241960f0e8f325f534d2406f42148a4486b5a`](https://github.com/livekit/agents-js/commit/33c241960f0e8f325f534d2406f42148a4486b5a)]:
  - @livekit/agents@0.7.1

## 0.6.1

### Patch Changes

- fix errors capturing frames - [#280](https://github.com/livekit/agents-js/pull/280) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`53092d01560db72abd7ed71e9e93a5217cca4ebd`](https://github.com/livekit/agents-js/commit/53092d01560db72abd7ed71e9e93a5217cca4ebd), [`827cf3e3662020f28cc6059a94045961dc342a53`](https://github.com/livekit/agents-js/commit/827cf3e3662020f28cc6059a94045961dc342a53)]:
  - @livekit/agents@0.6.4

## 0.6.0

### Minor Changes

- Add support for language code in 11Labs TTS package. - [#262](https://github.com/livekit/agents-js/pull/262) ([@FlorDonnaSanders](https://github.com/FlorDonnaSanders))

### Patch Changes

- Updated dependencies [[`7546a5b76729c90553142159bed0d14247486273`](https://github.com/livekit/agents-js/commit/7546a5b76729c90553142159bed0d14247486273)]:
  - @livekit/agents@0.6.3

## 0.5.4

### Patch Changes

- update rtc-node to 0.13.2 to fix issue with e2ee - [#258](https://github.com/livekit/agents-js/pull/258) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`dedb1cf139c8af4ce8709c86440c818157f5b475`](https://github.com/livekit/agents-js/commit/dedb1cf139c8af4ce8709c86440c818157f5b475), [`f3258b948539406213c15f8e817449b2588cde84`](https://github.com/livekit/agents-js/commit/f3258b948539406213c15f8e817449b2588cde84)]:
  - @livekit/agents@0.6.2

## 0.5.3

### Patch Changes

- allow any string for model - [#249](https://github.com/livekit/agents-js/pull/249) ([@nbsp](https://github.com/nbsp))

- add flash models - [#246](https://github.com/livekit/agents-js/pull/246) ([@sam-trost](https://github.com/sam-trost))

- Updated dependencies [[`4a66a82fc2fd0a25e30bdaa0bd095804c65ee101`](https://github.com/livekit/agents-js/commit/4a66a82fc2fd0a25e30bdaa0bd095804c65ee101), [`01aaa85445bbb8f30afe9c16360afb5a45c38e9e`](https://github.com/livekit/agents-js/commit/01aaa85445bbb8f30afe9c16360afb5a45c38e9e), [`4b7504654c73d9111d39e90d325d5f660b2c8ad9`](https://github.com/livekit/agents-js/commit/4b7504654c73d9111d39e90d325d5f660b2c8ad9)]:
  - @livekit/agents@0.6.1

## 0.5.2

### Patch Changes

- use AudioByteStream for TTS - [#227](https://github.com/livekit/agents-js/pull/227) ([@nbsp](https://github.com/nbsp))

- add metrics monitoring - [#227](https://github.com/livekit/agents-js/pull/227) ([@nbsp](https://github.com/nbsp))

- add testutils, tests for oai, 11labs - [#227](https://github.com/livekit/agents-js/pull/227) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0`](https://github.com/livekit/agents-js/commit/ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0), [`ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0`](https://github.com/livekit/agents-js/commit/ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0), [`ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0`](https://github.com/livekit/agents-js/commit/ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0), [`ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0`](https://github.com/livekit/agents-js/commit/ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0), [`ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0`](https://github.com/livekit/agents-js/commit/ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0), [`ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0`](https://github.com/livekit/agents-js/commit/ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0), [`ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0`](https://github.com/livekit/agents-js/commit/ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0), [`ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0`](https://github.com/livekit/agents-js/commit/ab0b7e81b12c8fcfea35accff8fd72f5cf6c43b0)]:
  - @livekit/agents@0.6.0

## 0.5.1

### Patch Changes

- add testutils, tests for oai, 11labs - [#206](https://github.com/livekit/agents-js/pull/206) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`67bad88bb59328fac03320e88c245871005ccc05`](https://github.com/livekit/agents-js/commit/67bad88bb59328fac03320e88c245871005ccc05), [`beb141f7de380d5a938347a2eda76d56f706333c`](https://github.com/livekit/agents-js/commit/beb141f7de380d5a938347a2eda76d56f706333c), [`8fa2b176bb6bdeba34430d59b23024d935f77453`](https://github.com/livekit/agents-js/commit/8fa2b176bb6bdeba34430d59b23024d935f77453)]:
  - @livekit/agents@0.5.1

## 0.5.0

### Minor Changes

- support native CommonJS - [#187](https://github.com/livekit/agents-js/pull/187) ([@nbsp](https://github.com/nbsp))

### Patch Changes

- chore(treewide): add READMEs for npmjs.com - [#187](https://github.com/livekit/agents-js/pull/187) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`9c9b73d3b9d3ed7b8ce071470492991dcd21d546`](https://github.com/livekit/agents-js/commit/9c9b73d3b9d3ed7b8ce071470492991dcd21d546), [`9c9b73d3b9d3ed7b8ce071470492991dcd21d546`](https://github.com/livekit/agents-js/commit/9c9b73d3b9d3ed7b8ce071470492991dcd21d546), [`9c9b73d3b9d3ed7b8ce071470492991dcd21d546`](https://github.com/livekit/agents-js/commit/9c9b73d3b9d3ed7b8ce071470492991dcd21d546)]:
  - @livekit/agents@0.5.0

## 0.4.6

### Patch Changes

- Add missing package info - [#172](https://github.com/livekit/agents-js/pull/172) ([@lukasIO](https://github.com/lukasIO))

- Updated dependencies [[`ad3c34823fc1955a4274e912ef0587d9b7f2218d`](https://github.com/livekit/agents-js/commit/ad3c34823fc1955a4274e912ef0587d9b7f2218d), [`1d74e20a0337e548af2cb87b64e131907648cc06`](https://github.com/livekit/agents-js/commit/1d74e20a0337e548af2cb87b64e131907648cc06), [`4aaec04857c623fef75ac6800fc9a078efdd4391`](https://github.com/livekit/agents-js/commit/4aaec04857c623fef75ac6800fc9a078efdd4391), [`4aaec04857c623fef75ac6800fc9a078efdd4391`](https://github.com/livekit/agents-js/commit/4aaec04857c623fef75ac6800fc9a078efdd4391)]:
  - @livekit/agents@0.4.6

## 0.4.5

### Patch Changes

- Use peer dependencies for @livekit/rtc-node and @livekit/agents - [#170](https://github.com/livekit/agents-js/pull/170) ([@lukasIO](https://github.com/lukasIO))

- Override with defaults if opts are provided - [#165](https://github.com/livekit/agents-js/pull/165) ([@gching](https://github.com/gching))

- Ensure token stream flushes - [#167](https://github.com/livekit/agents-js/pull/167) ([@gching](https://github.com/gching))

- Updated dependencies [[`f5dc9896a9eec2ba1e703d7209936bcc22d46b33`](https://github.com/livekit/agents-js/commit/f5dc9896a9eec2ba1e703d7209936bcc22d46b33), [`141519068094ca72f0fa86c4ee829ab3746bc02f`](https://github.com/livekit/agents-js/commit/141519068094ca72f0fa86c4ee829ab3746bc02f), [`6bff3b030063b2e851946b90ad9e7d981a46e2aa`](https://github.com/livekit/agents-js/commit/6bff3b030063b2e851946b90ad9e7d981a46e2aa), [`b719e7d5ffa37b541b219cd05c631483480e2103`](https://github.com/livekit/agents-js/commit/b719e7d5ffa37b541b219cd05c631483480e2103), [`1558b9bc4ed8ddc1c6b552875549a4fb96ec3802`](https://github.com/livekit/agents-js/commit/1558b9bc4ed8ddc1c6b552875549a4fb96ec3802)]:
  - @livekit/agents@0.4.5

## 0.4.4

### Patch Changes

- add ChunkedStream, openai.TTS - [#155](https://github.com/livekit/agents-js/pull/155) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`95ac1798daf846a14a4fb8b240412a7f66a897e6`](https://github.com/livekit/agents-js/commit/95ac1798daf846a14a4fb8b240412a7f66a897e6), [`cb500cb4319aab982d965c2ff118d2acbac965a8`](https://github.com/livekit/agents-js/commit/cb500cb4319aab982d965c2ff118d2acbac965a8), [`ddab1203ac56a88aa44defcc46f60b761b006292`](https://github.com/livekit/agents-js/commit/ddab1203ac56a88aa44defcc46f60b761b006292), [`cb500cb4319aab982d965c2ff118d2acbac965a8`](https://github.com/livekit/agents-js/commit/cb500cb4319aab982d965c2ff118d2acbac965a8)]:
  - @livekit/agents@0.4.4

## 0.4.3

### Patch Changes

- Updated dependencies []:
  - @livekit/agents@0.4.3

## 0.4.2

### Patch Changes

- Updated dependencies [[`38dd4c7d820de6faad512f0ac57c60f0fb1963be`](https://github.com/livekit/agents-js/commit/38dd4c7d820de6faad512f0ac57c60f0fb1963be)]:
  - @livekit/agents@0.4.2

## 0.4.1

### Patch Changes

- Updated dependencies [[`d2f1ef9f54cd7dd13892aa2ebe2f3f348b01afcb`](https://github.com/livekit/agents-js/commit/d2f1ef9f54cd7dd13892aa2ebe2f3f348b01afcb)]:
  - @livekit/agents@0.4.1

## 0.4.0

### Minor Changes

- re-add ElevenLabs TTS plugin - [#140](https://github.com/livekit/agents-js/pull/140) ([@nbsp](https://github.com/nbsp))

### Patch Changes

- Updated dependencies [[`d9273f27ae8df6c41e56a8258f540a4ffd9a7b7b`](https://github.com/livekit/agents-js/commit/d9273f27ae8df6c41e56a8258f540a4ffd9a7b7b), [`b4b9c1d337ff9a0212e90fb31002d4fcf58fe287`](https://github.com/livekit/agents-js/commit/b4b9c1d337ff9a0212e90fb31002d4fcf58fe287), [`4713a0490a3e4cd823aa648172c618ea924ae5d6`](https://github.com/livekit/agents-js/commit/4713a0490a3e4cd823aa648172c618ea924ae5d6), [`09782ec316590aa956d1a234dfb5594a8974dbfc`](https://github.com/livekit/agents-js/commit/09782ec316590aa956d1a234dfb5594a8974dbfc), [`d32f2470d4d1ea6786f4e13334f7251cc9823c04`](https://github.com/livekit/agents-js/commit/d32f2470d4d1ea6786f4e13334f7251cc9823c04), [`37bcf55aa75a289978af82e86332298dd7c07c73`](https://github.com/livekit/agents-js/commit/37bcf55aa75a289978af82e86332298dd7c07c73), [`35265a5b58adcbc048078d39c115412734dd4462`](https://github.com/livekit/agents-js/commit/35265a5b58adcbc048078d39c115412734dd4462), [`61899cde8c7fc19791d9d7d6a5ccabc2bb8f94dd`](https://github.com/livekit/agents-js/commit/61899cde8c7fc19791d9d7d6a5ccabc2bb8f94dd), [`644effb7c607e214e8d3ff8147afa7aec4a2496b`](https://github.com/livekit/agents-js/commit/644effb7c607e214e8d3ff8147afa7aec4a2496b), [`0e40262f3a60823d3d9cabbbd90f71c943db6c0e`](https://github.com/livekit/agents-js/commit/0e40262f3a60823d3d9cabbbd90f71c943db6c0e), [`cb788b7c37ca2297c04f5deb410a2fdab70d5c5f`](https://github.com/livekit/agents-js/commit/cb788b7c37ca2297c04f5deb410a2fdab70d5c5f), [`f844e8bb865beadbc58ea1c3a21e362aee1dce55`](https://github.com/livekit/agents-js/commit/f844e8bb865beadbc58ea1c3a21e362aee1dce55)]:
  - @livekit/agents@0.4.0
