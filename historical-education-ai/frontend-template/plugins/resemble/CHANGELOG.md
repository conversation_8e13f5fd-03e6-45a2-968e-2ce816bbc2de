# @livekit/agents-plugin-resemble

## 0.1.1

### Patch Changes

- fix: avoid masquerading types as mjs - [#425](https://github.com/livekit/agents-js/pull/425) ([@lukasIO](https://github.com/lukasIO))

- Updated dependencies [[`18a67fc7a0e1935fbebad4aa0af59414269e1cb1`](https://github.com/livekit/agents-js/commit/18a67fc7a0e1935fbebad4aa0af59414269e1cb1), [`2ec46bbe068b7ec1fb6338ed5156292b148b7c1b`](https://github.com/livekit/agents-js/commit/2ec46bbe068b7ec1fb6338ed5156292b148b7c1b), [`5306b7a3e3d1e7819fdf07a1f4fc77e32dd91dde`](https://github.com/livekit/agents-js/commit/5306b7a3e3d1e7819fdf07a1f4fc77e32dd91dde), [`3e57767f87a5bc79da7d364927ab149813c81314`](https://github.com/livekit/agents-js/commit/3e57767f87a5bc79da7d364927ab149813c81314)]:
  - @livekit/agents@0.7.7

## 0.1.0

### Minor Changes

- initial version - [#343](https://github.com/livekit/agents-js/pull/343) ([@nbsp](https://github.com/nbsp))

### Patch Changes

- Updated dependencies [[`d44445934cc291df987013068f5c43491634dfa1`](https://github.com/livekit/agents-js/commit/d44445934cc291df987013068f5c43491634dfa1), [`a7350c92f8968e0fd833e7679a607eaf9a1d7e7f`](https://github.com/livekit/agents-js/commit/a7350c92f8968e0fd833e7679a607eaf9a1d7e7f), [`2dcfeab76ace2e1851993771d769ebcb7c188144`](https://github.com/livekit/agents-js/commit/2dcfeab76ace2e1851993771d769ebcb7c188144), [`2bb936c55233ac0747582a5045caa595c6338651`](https://github.com/livekit/agents-js/commit/2bb936c55233ac0747582a5045caa595c6338651)]:
  - @livekit/agents@0.7.2
