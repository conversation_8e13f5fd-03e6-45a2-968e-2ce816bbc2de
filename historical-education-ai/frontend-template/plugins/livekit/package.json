{"name": "@livekit/agents-plugin-livekit", "version": "0.1.3", "description": "Additional utilities for LiveKit Node Agents", "main": "dist/index.js", "require": "dist/index.cjs", "types": "dist/index.d.ts", "exports": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "author": "LiveKit", "type": "module", "repository": "**************:livekit/agents-js.git", "license": "Apache-2.0", "files": ["dist", "src", "README.md"], "scripts": {"build": "tsup --onSuccess \"pnpm build:types\" && cp src/turn_detector.onnx dist/", "build:types": "tsc --declaration --emitDeclarationOnly && node ../../scripts/copyDeclarationOutput.js", "clean": "rm -rf dist", "clean:build": "pnpm clean && pnpm build", "lint": "eslint -f unix \"src/**/*.{ts,js}\"", "api:check": "api-extractor run --typescript-compiler-folder ../../node_modules/typescript", "api:update": "api-extractor run --local --typescript-compiler-folder ../../node_modules/typescript --verbose"}, "devDependencies": {"@livekit/agents": "workspace:^x", "@microsoft/api-extractor": "^7.35.0", "onnxruntime-common": ">=1.19.0 <1.22.0", "tsup": "^8.3.5", "typescript": "^5.0.0"}, "peerDependencies": {"@livekit/agents": "workspace:^x"}, "dependencies": {"@huggingface/transformers": "^3.2.1", "onnxruntime-node": ">=1.19.2 <1.22.0"}}