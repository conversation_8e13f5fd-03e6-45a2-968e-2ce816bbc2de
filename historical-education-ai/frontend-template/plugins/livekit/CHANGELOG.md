# @livekit/agents-plugin-livekit

## 0.1.3

### Patch Changes

- Don't support onnxruntime 1.22.0 - [#582](https://github.com/livekit/agents-js/pull/582) ([@bcherry](https://github.com/bcherry))

- Updated dependencies [[`133f2a84b406527a6c7d0e35d62b2ef7c581b0db`](https://github.com/livekit/agents-js/commit/133f2a84b406527a6c7d0e35d62b2ef7c581b0db)]:
  - @livekit/agents@0.7.9

## 0.1.2

### Patch Changes

- fix: avoid masquerading types as mjs - [#425](https://github.com/livekit/agents-js/pull/425) ([@lukasIO](https://github.com/lukasIO))

- Updated dependencies [[`18a67fc7a0e1935fbebad4aa0af59414269e1cb1`](https://github.com/livekit/agents-js/commit/18a67fc7a0e1935fbebad4aa0af59414269e1cb1), [`2ec46bbe068b7ec1fb6338ed5156292b148b7c1b`](https://github.com/livekit/agents-js/commit/2ec46bbe068b7ec1fb6338ed5156292b148b7c1b), [`5306b7a3e3d1e7819fdf07a1f4fc77e32dd91dde`](https://github.com/livekit/agents-js/commit/5306b7a3e3d1e7819fdf07a1f4fc77e32dd91dde), [`3e57767f87a5bc79da7d364927ab149813c81314`](https://github.com/livekit/agents-js/commit/3e57767f87a5bc79da7d364927ab149813c81314)]:
  - @livekit/agents@0.7.7

## 0.1.1

### Patch Changes

- cleanup resources with onnx runtime - [#377](https://github.com/livekit/agents-js/pull/377) ([@Shubhrakanti](https://github.com/Shubhrakanti))

- Updated dependencies [[`ae508d76d6b521218f8320f8dba1b1d0fdad79a2`](https://github.com/livekit/agents-js/commit/ae508d76d6b521218f8320f8dba1b1d0fdad79a2), [`e1dd8f86660b4f96585833dc896b719dd3ad54b2`](https://github.com/livekit/agents-js/commit/e1dd8f86660b4f96585833dc896b719dd3ad54b2)]:
  - @livekit/agents@0.7.5

## 0.1.0

### Minor Changes

- feat: add turn detector - [#225](https://github.com/livekit/agents-js/pull/225) ([@nbsp](https://github.com/nbsp))

### Patch Changes

- Updated dependencies [[`4681792123ebf7eb6f75d89efe32ec11cb1ee179`](https://github.com/livekit/agents-js/commit/4681792123ebf7eb6f75d89efe32ec11cb1ee179), [`3e1b2d0fd07a5fab53bf20c151faad3fd9bfa77d`](https://github.com/livekit/agents-js/commit/3e1b2d0fd07a5fab53bf20c151faad3fd9bfa77d), [`b0fa6007372dc798e222487e87f7b80f1a64ac4e`](https://github.com/livekit/agents-js/commit/b0fa6007372dc798e222487e87f7b80f1a64ac4e), [`c2794335d5395744e9ba0c6691a4ff6bb7c28e40`](https://github.com/livekit/agents-js/commit/c2794335d5395744e9ba0c6691a4ff6bb7c28e40), [`a3d025047e62d89e935b878502735a0768076d7c`](https://github.com/livekit/agents-js/commit/a3d025047e62d89e935b878502735a0768076d7c), [`629c737098b6b6636356527bbe8a4e81d8b6f047`](https://github.com/livekit/agents-js/commit/629c737098b6b6636356527bbe8a4e81d8b6f047)]:
  - @livekit/agents@0.7.0
