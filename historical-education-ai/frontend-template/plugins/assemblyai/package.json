{"name": "@livekit/agents-plugin-assemblyai", "version": "0.0.0", "description": "AssemblyAI plugin for LiveKit Agents for Node.js", "main": "dist/index.js", "require": "dist/index.cjs", "types": "dist/index.d.ts", "exports": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "author": "LiveKit", "type": "module", "repository": "**************:livekit/agents-js.git", "license": "Apache-2.0", "files": ["dist", "src", "README.md"], "scripts": {"build": "tsup --onSuccess \"pnpm build:types\"", "build:types": "tsc --declaration --emitDeclarationOnly && node ../../scripts/copyDeclarationOutput.js", "clean": "rm -rf dist", "clean:build": "pnpm clean && pnpm build", "lint": "eslint -f unix \"src/**/*.{ts,js}\"", "api:check": "api-extractor run --typescript-compiler-folder ../../node_modules/typescript", "api:update": "api-extractor run --local --typescript-compiler-folder ../../node_modules/typescript --verbose"}, "devDependencies": {"@livekit/agents": "workspace:^x", "@livekit/agents-plugin-silero": "workspace:^x", "@livekit/agents-plugins-test": "workspace:^x", "@livekit/rtc-node": "^0.13.4", "@microsoft/api-extractor": "^7.35.0", "tsup": "^8.3.5", "typescript": "^5.0.0"}, "dependencies": {"assemblyai": "^4.9.0"}, "peerDependencies": {"@livekit/agents": "workspace:^x", "@livekit/rtc-node": "^0.13.4"}}