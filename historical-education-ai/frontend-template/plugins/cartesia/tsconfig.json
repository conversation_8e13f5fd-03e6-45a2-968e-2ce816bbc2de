{
  "extends": "../../tsconfig.json",
  "include": ["./src"],
  "compilerOptions": {
    // match output dir to input dir. e.g. dist/index instead of dist/src/index
    "rootDir": "./src",
    "declarationDir": "./dist",
    "outDir": "./dist"
  },
  "typedocOptions": {
    "name": "plugins/agents-plugin-cartesia",
    "entryPointStrategy": "resolve",
    "readme": "none",
    "entryPoints": ["src/index.ts"]
  }
}
