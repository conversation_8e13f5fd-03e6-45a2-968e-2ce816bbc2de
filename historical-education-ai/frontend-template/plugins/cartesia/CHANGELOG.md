# @livekit/agents-plugin-cartesia

## 0.1.4

### Patch Changes

- fix: avoid masquerading types as mjs - [#425](https://github.com/livekit/agents-js/pull/425) ([@lukas<PERSON>](https://github.com/lukasIO))

- Updated dependencies [[`18a67fc7a0e1935fbebad4aa0af59414269e1cb1`](https://github.com/livekit/agents-js/commit/18a67fc7a0e1935fbebad4aa0af59414269e1cb1), [`2ec46bbe068b7ec1fb6338ed5156292b148b7c1b`](https://github.com/livekit/agents-js/commit/2ec46bbe068b7ec1fb6338ed5156292b148b7c1b), [`5306b7a3e3d1e7819fdf07a1f4fc77e32dd91dde`](https://github.com/livekit/agents-js/commit/5306b7a3e3d1e7819fdf07a1f4fc77e32dd91dde), [`3e57767f87a5bc79da7d364927ab149813c81314`](https://github.com/livekit/agents-js/commit/3e57767f87a5bc79da7d364927ab149813c81314)]:
  - @livekit/agents@0.7.7

## 0.1.3

### Patch Changes

- add updateOptions - [#375](https://github.com/livekit/agents-js/pull/375) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`5f805961b91f121c4a7d83a04b648b1122b15dba`](https://github.com/livekit/agents-js/commit/5f805961b91f121c4a7d83a04b648b1122b15dba), [`a0c49c9b7b771bb67748fb7c75c5dd2fe9f90e8a`](https://github.com/livekit/agents-js/commit/a0c49c9b7b771bb67748fb7c75c5dd2fe9f90e8a), [`c06c84fa79ecdfae9170a5255a10315cf64216aa`](https://github.com/livekit/agents-js/commit/c06c84fa79ecdfae9170a5255a10315cf64216aa)]:
  - @livekit/agents@0.7.4

## 0.1.2

### Patch Changes

- bump to use 0.7.0 - [#316](https://github.com/livekit/agents-js/pull/316) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`724c02bb7a91c27d6c8daf961842fb9f0934770c`](https://github.com/livekit/agents-js/commit/724c02bb7a91c27d6c8daf961842fb9f0934770c), [`7398cffad62b17c79b5fe2f0ca4e99e548560367`](https://github.com/livekit/agents-js/commit/7398cffad62b17c79b5fe2f0ca4e99e548560367), [`6ed0c90d1bab013854c416768b10ef96f3227d68`](https://github.com/livekit/agents-js/commit/6ed0c90d1bab013854c416768b10ef96f3227d68), [`33c241960f0e8f325f534d2406f42148a4486b5a`](https://github.com/livekit/agents-js/commit/33c241960f0e8f325f534d2406f42148a4486b5a)]:
  - @livekit/agents@0.7.1

## 0.1.1

### Patch Changes

- update rtc-node to 0.13.2 to fix issue with e2ee - [#258](https://github.com/livekit/agents-js/pull/258) ([@nbsp](https://github.com/nbsp))

- Updated dependencies [[`dedb1cf139c8af4ce8709c86440c818157f5b475`](https://github.com/livekit/agents-js/commit/dedb1cf139c8af4ce8709c86440c818157f5b475), [`f3258b948539406213c15f8e817449b2588cde84`](https://github.com/livekit/agents-js/commit/f3258b948539406213c15f8e817449b2588cde84)]:
  - @livekit/agents@0.6.2

## 0.1.0

### Minor Changes

- plugins(cartesia): init with TTS - [#217](https://github.com/livekit/agents-js/pull/217) ([@nbsp](https://github.com/nbsp))

### Patch Changes

- Updated dependencies [[`4a66a82fc2fd0a25e30bdaa0bd095804c65ee101`](https://github.com/livekit/agents-js/commit/4a66a82fc2fd0a25e30bdaa0bd095804c65ee101), [`01aaa85445bbb8f30afe9c16360afb5a45c38e9e`](https://github.com/livekit/agents-js/commit/01aaa85445bbb8f30afe9c16360afb5a45c38e9e), [`4b7504654c73d9111d39e90d325d5f660b2c8ad9`](https://github.com/livekit/agents-js/commit/4b7504654c73d9111d39e90d325d5f660b2c8ad9)]:
  - @livekit/agents@0.6.1
