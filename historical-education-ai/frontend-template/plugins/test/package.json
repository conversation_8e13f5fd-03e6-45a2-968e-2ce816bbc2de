{"name": "@livekit/agents-plugins-test", "version": "0.0.1", "description": "Testing suite for LiveKit Agents plugins", "author": "LiveKit", "type": "module", "private": true, "repository": "**************:livekit/agents-js.git", "license": "Apache-2.0", "main": "dist/index.js", "require": "dist/index.cjs", "types": "dist/index.d.ts", "exports": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "files": ["src", "dist", "README.md"], "scripts": {"build": "tsup --onSuccess \"pnpm build:types\" && cp src/long.wav dist/", "build:types": "tsc --declaration --emitDeclarationOnly && node ../../scripts/copyDeclarationOutput.js", "lint": "eslint -f unix \"src/**/*.{ts,js}\""}, "devDependencies": {"@livekit/agents": "workspace:^x", "@livekit/rtc-node": "^0.13.11", "@types/node": "^22.5.5", "tsup": "^8.3.5", "typescript": "^5.0.0"}, "dependencies": {"fastest-levenshtein": "^1.0.16", "vitest": "^1.6.0", "zod": "^3.23.8"}, "peerDependencies": {"@livekit/agents": "workspace:^x", "@livekit/rtc-node": "^0.13.11"}}