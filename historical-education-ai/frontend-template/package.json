{"name": "@livekit/agents-monorepo", "private": true, "type": "module", "scripts": {"build": "turbo run build", "clean": "turbo run clean", "clean:build": "turbo run clean:build", "build:agents": "turbo run build --filter=@livekit/agents...", "build:plugins": "turbo run build --filter=@livekit/agents-plugin-*...", "ci:publish": "pnpm build && changeset publish", "api:check": "turbo run api:check", "api:update": "turbo run api:update", "format:check": "prettier --check \"**/src/**/*.{ts,tsx,md,json}\"", "format:write": "prettier --write \"**/src/**/*.{ts,tsx,md,json}\"", "lint": "turbo lint", "lint:fix": "turbo lint -- --fix", "test": "vitest run", "test:watch": "vitest", "doc": "typedoc && mkdir -p docs/assets/github && cp .github/*.png docs/assets/github/ && find docs -name '*.html' -type f -exec sed -i.bak 's|=\"/.github/|=\"assets/github/|g' {} + && find docs -name '*.bak' -delete", "examples:minimal": "pnpm exec tsx examples/src/multimodal_agent.ts"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@livekit/changesets-changelog-github": "^0.0.4", "@rushstack/heft": "^0.66.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "eslint-config-prettier": "^8.10.0", "eslint-config-standard": "^17.1.0", "eslint-config-turbo": "^1.12.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-tsdoc": "^0.2.17", "prettier": "^3.2.5", "turbo": "^1.13.3", "typedoc": "^0.25.13", "typescript": "^5.4.5", "vitest": "^1.6.0", "tsup": "^8.4.0"}, "packageManager": "pnpm@9.7.0"}