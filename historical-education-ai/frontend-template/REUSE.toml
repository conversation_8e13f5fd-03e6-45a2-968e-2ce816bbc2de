# SPDX-FileCopyrightText: 2024 LiveKit, Inc.
#
# SPDX-License-Identifier: Apache-2.0

version = 1
SPDX-PackageName = "agents-js"
SPDX-PackageSupplier = "LiveKit, Inc. <https://livekit.io>"
SPDX-PackageDownloadLocation = "https://github.com/livekit/agents-js"

# trivial files
[[annotations]]
path = [".gitignore", "flake.lock", ".envrc", "packages/livekit-rtc/.gitignore", ".changeset/**", "**/CHANGELOG.md", "NOTICE", ".github/**"]
SPDX-FileCopyrightText = "2024 LiveKit, Inc."
SPDX-License-Identifier = "Apache-2.0"

# pnpm files
[[annotations]]
path = ["pnpm-workspace.yaml", "pnpm-lock.yaml"]
SPDX-FileCopyrightText = "2024 LiveKit, Inc."
SPDX-License-Identifier = "Apache-2.0"

# project configuration files
[[annotations]]
path = [".prettierrc", ".prettierignore", ".eslintrc", "**.json", "**/tsup.config.ts"]
SPDX-FileCopyrightText = "2024 LiveKit, Inc."
SPDX-License-Identifier = "Apache-2.0"

# silero onnx file
[[annotations]]
path = ["**/silero_vad.onnx"]
SPDX-FileCopyrightText = "2024 Silero Team"
SPDX-License-Identifier = "CC-BY-NC-SA-4.0"

# turn detector onnx file
[[annotations]]
path = ["**/turn_detector.onnx"]
SPDX-FileCopyrightText = "2024 LiveKit, Inc."
SPDX-License-Identifier = "LicenseRef-LiveKitModelLicense"

# testing files
[[annotations]]
path = ["**/.gitattributes", "**.wav"]
SPDX-FileCopyrightText = "2024 LiveKit, Inc."
SPDX-License-Identifier = "Apache-2.0"
