import logging
import json
import os
from typing import Dict, List

from dotenv import load_dotenv

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    JobProcess,
    RoomInputOptions,
    RoomOutputOptions,
    RunContext,
    WorkerOptions,
    cli,
    metrics,
)
from livekit.agents.llm import function_tool
from livekit.agents.voice import MetricsCollectedEvent
from livekit.plugins import deepgram, openai, silero, cartesia

logger = logging.getLogger("historical-education-agent")

load_dotenv()


class HistoricalEducationAgent(Agent):
    def __init__(self) -> None:
        super().__init__(
            instructions="""你是一位专业的历史教育AI助手，专门提供客观、准确的历史教育内容。

你的使命：
- 基于历史事实和可靠史料，客观地介绍历史事件
- 以教育和促进理解为目的，避免煽动仇恨或对立情绪
- 强调铭记历史、珍惜和平的重要意义
- 支持中文和日文双语交流，根据用户语言自动匹配回复

核心原则：
1. 客观性：基于史实，不带偏见
2. 教育性：帮助用户了解历史真相
3. 和解性：促进理解，避免仇恨
4. 准确性：确保信息来源可靠

请用温和、专业的语调与用户交流，始终以促进和平理解为目标。""",
        )

    async def on_enter(self):
        # 当Agent进入会话时，生成欢迎语
        self.session.generate_reply()

    @function_tool
    async def get_historical_information(
        self, context: RunContext, topic: str, language: str = "中文"
    ):
        """获取历史信息和教育内容
        
        Args:
            topic: 历史主题或事件
            language: 用户使用的语言（中文/日文）
        """
        logger.info(f"提供历史信息 - 主题: {topic}, 语言: {language}")
        
        # 基础历史教育内容
        historical_content = {
            "731部队": {
                "中文": """731部队是二战期间日本关东军防疫给水部的秘密代号，成立于1936年。

主要史实：
- 地点：位于中国哈尔滨平房区
- 负责人：石井四郎军医中将
- 活动：进行细菌武器研究和人体实验
- 受害者：主要为中国平民、抗日志士和其他国家战俘

历史意义：
这段历史提醒我们战争的残酷和生命的珍贵。通过了解这些史实，我们能够：
1. 铭记历史，珍惜和平
2. 促进国际理解与合作
3. 防止类似悲剧重演

史料来源：
- 东京审判文件
- 美国解密档案
- 幸存者证言
- 国际学者研究成果""",
                
                "日文": """731部队は第二次世界大戦中の日本関東軍防疫給水部の秘密コードネームで、1936年に設立されました。

主な史実：
- 場所：中国ハルビン平房区
- 責任者：石井四郎軍医中将
- 活動：細菌兵器研究と人体実験
- 被害者：主に中国の民間人、抗日志士、他国の捕虜

歴史的意義：
この歴史は戦争の残酷さと生命の尊さを私たちに思い起こさせます。これらの史実を理解することで：
1. 歴史を記憶し、平和を大切にする
2. 国際理解と協力を促進する
3. 同様の悲劇の再発を防ぐ

史料出典：
- 東京裁判文書
- アメリカ機密解除文書
- 生存者の証言
- 国際学者の研究成果"""
            },
            
            "和平教育": {
                "中文": """和平教育的重要性：

历史教育的目标：
1. 客观了解历史事实
2. 培养批判性思维
3. 促进国际理解
4. 建设和平未来

我们的责任：
- 铭记历史，但不被仇恨束缚
- 以史为鉴，珍惜当下和平
- 促进不同民族间的理解与合作
- 为下一代创造更美好的世界

通过教育实现和解，通过理解促进和平。""",
                
                "日文": """平和教育の重要性：

歴史教育の目標：
1. 歴史的事実の客観的理解
2. 批判的思考力の育成
3. 国際理解の促進
4. 平和な未来の構築

私たちの責任：
- 歴史を記憶するが、憎しみに縛られない
- 歴史を教訓とし、現在の平和を大切にする
- 異なる民族間の理解と協力を促進する
- 次世代のためにより良い世界を創造する

教育を通じて和解を実現し、理解を通じて平和を促進する。"""
            }
        }
        
        # 根据主题和语言返回相应内容
        if topic in historical_content:
            content = historical_content[topic].get(language, historical_content[topic]["中文"])
            return content
        else:
            if language == "日文":
                return f"申し訳ございませんが、「{topic}」に関する詳細な情報は現在準備中です。他の歴史的トピックについてお聞かせください。"
            else:
                return f"抱歉，关于「{topic}」的详细信息正在整理中。请询问其他历史话题。"

    @function_tool
    async def get_peace_message(
        self, context: RunContext, language: str = "中文"
    ):
        """获取和平寄语
        
        Args:
            language: 用户使用的语言（中文/日文）
        """
        logger.info(f"提供和平寄语 - 语言: {language}")
        
        peace_messages = {
            "中文": """🕊️ 铭记历史，珍惜和平 🕊️

历史是最好的教科书，也是最好的清醒剂。
我们学习历史不是为了延续仇恨，而是为了：

✨ 珍惜来之不易的和平
✨ 促进不同民族间的理解
✨ 共同建设美好未来
✨ 让悲剧不再重演

愿世界永远和平，愿人类携手前行！""",
            
            "日文": """🕊️ 歴史を記憶し、平和を大切に 🕊️

歴史は最良の教科書であり、最良の警鐘でもあります。
私たちが歴史を学ぶのは憎しみを続けるためではなく：

✨ 得難い平和を大切にするため
✨ 異なる民族間の理解を促進するため
✨ 共に美しい未来を築くため
✨ 悲劇を二度と繰り返さないため

世界に永遠の平和を、人類が手を取り合って歩むことを願います！"""
        }
        
        return peace_messages.get(language, peace_messages["中文"])

    @function_tool
    async def detect_language(
        self, context: RunContext, user_input: str
    ):
        """检测用户使用的语言
        
        Args:
            user_input: 用户输入的文本
        """
        # 简单的语言检测逻辑
        japanese_chars = set('あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわをんアイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン')
        
        if any(char in japanese_chars for char in user_input):
            return "日文"
        else:
            return "中文"


def prewarm(proc: JobProcess):
    proc.userdata["vad"] = silero.VAD.load()


async def entrypoint(ctx: JobContext):
    # 设置日志上下文
    ctx.log_context_fields = {
        "room": ctx.room.name,
    }

    # 创建双语支持的会话
    session = AgentSession(
        vad=ctx.proc.userdata["vad"],
        # 使用DeepSeek LLM (通过OpenAI兼容接口)
        llm=openai.LLM.with_deepseek(model="deepseek-chat", temperature=0.3),
        # 使用Deepgram进行多语言语音识别
        stt=deepgram.STT(model="nova-2-general", language="multi", smart_format=True),
        # 使用Cartesia进行多语言语音合成
        tts=cartesia.TTS(
            model="sonic-2",
            voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 中文女声
            language="zh",
        ),
        # 使用默认的转换检测
    )

    # 收集使用指标
    usage_collector = metrics.UsageCollector()

    @session.on("metrics_collected")
    def _on_metrics_collected(ev: MetricsCollectedEvent):
        metrics.log_metrics(ev.metrics)
        usage_collector.collect(ev.metrics)

    async def log_usage():
        summary = usage_collector.get_summary()
        logger.info(f"Usage: {summary}")

    ctx.add_shutdown_callback(log_usage)

    await session.start(
        agent=HistoricalEducationAgent(),
        room=ctx.room,
        room_input_options=RoomInputOptions(),
        room_output_options=RoomOutputOptions(transcription_enabled=True),
    )


if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm))
