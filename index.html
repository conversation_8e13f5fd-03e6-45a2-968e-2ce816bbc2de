<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史教育AI博物馆 - 云归.中国</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            text-decoration: none;
        }

        .logo .brand {
            color: #e74c3c;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            text-align: center;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2rem;
            margin-bottom: 2rem;
            max-width: 600px;
            line-height: 1.6;
        }

        .voice-interface {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 500px;
            width: 100%;
        }

        .status {
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 1.5rem;
        }

        .voice-button {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
            margin: 0 auto 1.5rem;
            display: block;
        }

        .voice-button:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
        }

        .voice-button.active {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .peace-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(44, 62, 80, 0.1);
        }

        .peace-button {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(46, 204, 113, 0.3);
        }

        .peace-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
        }

        .peace-count {
            margin-top: 1rem;
            color: #27ae60;
            font-weight: bold;
        }

        .language-toggle {
            margin-top: 1rem;
        }

        .lang-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lang-btn.active {
            background: #2980b9;
        }

        .footer {
            background: rgba(0, 0, 0, 0.1);
            color: white;
            text-align: center;
            padding: 1rem;
            font-size: 0.9rem;
        }

        .ai-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="#" class="logo">
            <span class="brand">云归</span>.中国
        </a>
        <div class="ai-indicator">🤖 AI历史博物馆</div>
    </div>

    <div class="main-container">
        <h1 class="title">历史教育AI助手</h1>
        <p class="subtitle">
            基于历史事实，客观展示历史真相<br>
            以教育促理解，以真相促和平
        </p>

        <div class="voice-interface">
            <div class="status" id="status">点击开始语音对话</div>
            
            <button class="voice-button" id="voiceBtn">
                🎤<br>开始对话
            </button>

            <div class="language-toggle">
                <button class="lang-btn active" onclick="setLanguage('zh')">中文</button>
                <button class="lang-btn" onclick="setLanguage('ja')">日本語</button>
            </div>

            <div class="peace-section">
                <button class="peace-button" onclick="supportPeace()">
                    🕊️ 铭记历史，珍惜和平
                </button>
                <div class="peace-count" id="peaceCount">
                    已有 <span id="count">0</span> 人支持和平
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>历史教育AI博物馆 | 以史为鉴，珍惜和平 | 促进理解，共建未来</p>
    </div>

    <script>
        let currentLanguage = 'zh';
        let peaceCount = parseInt(localStorage.getItem('peaceCount') || '0');
        let isConnected = false;
        
        // 更新和平支持计数
        document.getElementById('count').textContent = peaceCount;

        function setLanguage(lang) {
            currentLanguage = lang;
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            if (lang === 'ja') {
                document.querySelector('.title').textContent = '歴史教育AIアシスタント';
                document.querySelector('.subtitle').innerHTML = '歴史的事実に基づき、客観的に歴史の真実を展示<br>教育を通じて理解を促進し、真実を通じて平和を促進';
                document.querySelector('.peace-button').innerHTML = '🕊️ 歴史を記憶し、平和を大切に';
                document.getElementById('status').textContent = 'クリックして音声対話を開始';
                document.querySelector('.voice-button').innerHTML = '🎤<br>対話開始';
            } else {
                document.querySelector('.title').textContent = '历史教育AI助手';
                document.querySelector('.subtitle').innerHTML = '基于历史事实，客观展示历史真相<br>以教育促理解，以真相促和平';
                document.querySelector('.peace-button').innerHTML = '🕊️ 铭记历史，珍惜和平';
                document.getElementById('status').textContent = '点击开始语音对话';
                document.querySelector('.voice-button').innerHTML = '🎤<br>开始对话';
            }
        }

        function supportPeace() {
            peaceCount++;
            document.getElementById('count').textContent = peaceCount;
            localStorage.setItem('peaceCount', peaceCount.toString());
            
            // 显示感谢消息
            const btn = document.querySelector('.peace-button');
            const originalText = btn.innerHTML;
            btn.innerHTML = currentLanguage === 'ja' ? '✨ ありがとうございます！' : '✨ 感谢您的支持！';
            btn.style.background = 'linear-gradient(45deg, #f39c12, #e67e22)';
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.style.background = 'linear-gradient(45deg, #2ecc71, #27ae60)';
            }, 2000);
        }

        // 模拟语音连接功能
        document.getElementById('voiceBtn').addEventListener('click', function() {
            if (!isConnected) {
                // 开始连接
                this.classList.add('active');
                this.innerHTML = currentLanguage === 'ja' ? '🔄<br>接続中...' : '🔄<br>连接中...';
                document.getElementById('status').textContent = currentLanguage === 'ja' ? 'AIアシスタントに接続中...' : '正在连接AI助手...';
                
                // 模拟连接过程
                setTimeout(() => {
                    isConnected = true;
                    this.innerHTML = currentLanguage === 'ja' ? '🎙️<br>話してください' : '🎙️<br>请说话';
                    this.style.background = 'linear-gradient(45deg, #2ecc71, #27ae60)';
                    document.getElementById('status').textContent = currentLanguage === 'ja' ? 'AIアシスタントが聞いています...' : 'AI助手正在聆听...';
                }, 2000);
            } else {
                // 断开连接
                isConnected = false;
                this.classList.remove('active');
                this.innerHTML = currentLanguage === 'ja' ? '🎤<br>対話開始' : '🎤<br>开始对话';
                this.style.background = 'linear-gradient(45deg, #ff6b6b, #ee5a24)';
                document.getElementById('status').textContent = currentLanguage === 'ja' ? 'クリックして音声対話を開始' : '点击开始语音对话';
            }
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('历史教育AI博物馆已加载');
        });
    </script>
</body>
</html>
