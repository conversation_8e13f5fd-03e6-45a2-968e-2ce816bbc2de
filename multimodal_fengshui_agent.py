#!/usr/bin/env python3
"""
LiveKit风水AI助手 - 多模态图片分析版本
基于LiveKit官方开发指南实现的图片分析功能

支持功能：
1. 前端图片上传分析
2. 知识库查询
3. 网络图片搜索
4. 服务器截图分析
"""

import asyncio
import base64
import json
import os
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
)
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️ 知识库加载失败: {e}")
        return {}

FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()

class MultimodalFengshuiAgent(Agent):
    """多模态风水AI助手 - 支持图片分析"""
    
    def __init__(self) -> None:
        self._tasks = []  # 防止垃圾回收
        
        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师，具备图片分析能力。

🎯 你的专长：
- 住宅风水：房屋布局、朝向、装修建议
- 商业风水：办公室、店铺、工厂布局
- 图片分析：分析用户上传的房屋、办公室等图片
- 色彩搭配：根据风水理论推荐颜色方案

📷 图片分析能力：
当用户上传图片时，请详细分析：
1. 房间的整体布局和空间感
2. 家具的摆放位置和朝向
3. 颜色搭配和装饰元素
4. 采光和通风情况
5. 可能存在的风水问题
6. 具体的改善建议

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释风水概念
- 给出具体可行的建议
- 结合传统风水理论和现代生活实际

请用中文与用户交流，提供专业的风水咨询服务。
""",
        )
    
    async def on_enter(self):
        """Agent进入房间时的初始化"""
        def _image_received_handler(reader, participant_identity):
            task = asyncio.create_task(
                self._image_received(reader, participant_identity)
            )
            self._tasks.append(task)
            task.add_done_callback(lambda t: self._tasks.remove(t))
        
        # 注册图片接收处理器 - 基于官方开发指南
        try:
            from livekit.agents import get_job_context
            get_job_context().room.register_byte_stream_handler("images", _image_received_handler)
            print("✅ 图片上传处理器已注册")
        except Exception as e:
            print(f"⚠️ 图片处理器注册失败: {e}")
    
    async def _image_received(self, reader, participant_identity):
        """处理接收到的图片 - 基于官方Vision文档"""
        print(f"📷 接收到来自 {participant_identity} 的图片")
        
        image_bytes = bytes()
        async for chunk in reader:
            image_bytes += chunk
        
        print(f"📊 图片大小: {len(image_bytes)} bytes")
        
        # 将图片添加到聊天上下文 - 使用官方ImageContent
        try:
            from livekit.agents.llm import ImageContent
            
            chat_ctx = self.chat_ctx.copy()
            chat_ctx.add_message(
                role="user",
                content=[
                    "请分析这张图片的风水布局，并提供专业建议：",
                    ImageContent(
                        image=f"data:image/png;base64,{base64.b64encode(image_bytes).decode('utf-8')}",
                        inference_detail="high"  # 使用高质量推理
                    )
                ],
            )
            
            await self.update_chat_ctx(chat_ctx)
            print("✅ 图片已添加到聊天上下文，准备分析")
            
        except Exception as e:
            print(f"❌ 图片处理失败: {e}")
            # 降级处理：提供语音反馈
            await self._provide_fallback_response()
    
    async def _provide_fallback_response(self):
        """当图片处理失败时的降级响应"""
        try:
            from livekit.agents import get_job_context
            session = get_job_context().session
            await session.say("抱歉，图片处理遇到了问题。请您描述一下房间的布局，我可以为您提供语音风水咨询。")
        except Exception as e:
            print(f"❌ 降级响应失败: {e}")
    
    @function_tool()
    async def search_fengshui_knowledge(self, query: str) -> str:
        """搜索风水知识库
        
        Args:
            query: 搜索关键词，如"客厅布局"、"办公室风水"等
        """
        try:
            results = []
            for category, items in FENGSHUI_KNOWLEDGE.items():
                if isinstance(items, dict):
                    for key, value in items.items():
                        if query.lower() in key.lower() or query.lower() in str(value).lower():
                            results.append(f"【{category}】{key}: {value}")
            
            if results:
                return "根据风水知识库，找到以下相关信息：\n" + "\n".join(results[:3])
            else:
                return f"未在知识库中找到关于'{query}'的具体信息，但我可以基于传统风水理论为您提供建议。"
                
        except Exception as e:
            return f"知识库查询出现问题：{e}，但我仍可以基于专业经验为您提供风水建议。"
    
    @function_tool()
    async def analyze_room_layout(self, room_type: str, description: str) -> str:
        """分析房间布局的风水
        
        Args:
            room_type: 房间类型，如"客厅"、"卧室"、"办公室"等
            description: 房间描述，包括朝向、布局、颜色等
        """
        # 调用知识库搜索
        knowledge_result = await self.search_fengshui_knowledge(room_type)
        
        analysis = f"""
🏠 **{room_type}风水分析**

**基本情况**：{description}

**专业建议**：
{knowledge_result}

**改善方案**：
1. 检查房间的采光和通风情况
2. 确保家具摆放符合风水原理
3. 选择合适的颜色搭配
4. 注意避免风水禁忌

💡 **提示**：如果您能提供房间的照片，我可以给出更精确的分析和建议。
        """
        
        return analysis
    
    @function_tool()
    async def get_color_advice(self, color: str) -> str:
        """获取颜色风水建议

        Args:
            color: 颜色名称，如"红色"、"蓝色"等
        """
        if "color_advice" in FENGSHUI_KNOWLEDGE:
            color_data = FENGSHUI_KNOWLEDGE["color_advice"].get(color)
            if color_data:
                advice = f"【{color}在风水中的寓意】\n"
                advice += f"象征意义：{color_data.get('寓意', '无特殊寓意')}\n"
                advice += f"适用场所：{color_data.get('适用', '无特殊要求')}\n"
                advice += f"使用禁忌：{color_data.get('禁忌', '无特殊禁忌')}"
                return advice

        return f"抱歉，暂时没有关于{color}的详细风水建议，但我可以基于传统理论为您分析。"

    @function_tool()
    async def search_reference_images(self, query: str) -> str:
        """搜索参考图片进行风水分析

        Args:
            query: 搜索关键词，如"客厅布局"、"办公室设计"等
        """
        try:
            import requests
            from urllib.parse import quote

            # 使用免费的图片搜索API (示例)
            search_url = f"https://api.unsplash.com/search/photos"
            params = {
                'query': f"{query} interior design",
                'per_page': 3,
                'client_id': os.getenv('UNSPLASH_ACCESS_KEY', 'demo')
            }

            response = requests.get(search_url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                images = data.get('results', [])

                if images:
                    result = f"找到{len(images)}张关于'{query}'的参考图片：\n"
                    for i, img in enumerate(images[:3], 1):
                        result += f"{i}. {img.get('description', '室内设计图片')}\n"
                        # 可以将图片URL添加到聊天上下文进行分析
                        await self._analyze_reference_image(img['urls']['regular'])
                    return result
                else:
                    return f"未找到关于'{query}'的参考图片，但我可以基于经验为您提供建议。"
            else:
                return "图片搜索服务暂时不可用，请直接上传您的图片进行分析。"

        except Exception as e:
            print(f"❌ 图片搜索失败: {e}")
            return "图片搜索功能暂时不可用，请直接描述您的问题或上传图片。"

    async def _analyze_reference_image(self, image_url: str):
        """分析参考图片"""
        try:
            from livekit.agents.llm import ImageContent

            chat_ctx = self.chat_ctx.copy()
            chat_ctx.add_message(
                role="user",
                content=[
                    f"请分析这张参考图片的风水布局：",
                    ImageContent(image=image_url, inference_detail="high")
                ],
            )

            await self.update_chat_ctx(chat_ctx)
            print(f"✅ 参考图片已添加到分析队列: {image_url}")

        except Exception as e:
            print(f"❌ 参考图片分析失败: {e}")

    @function_tool()
    async def capture_server_screenshot(self, description: str) -> str:
        """捕获服务器保存的截图进行分析

        Args:
            description: 截图描述，用于定位文件
        """
        try:
            # 查找服务器上的截图文件
            screenshot_dir = "/www/wwwroot/su.guiyunai.fun/screenshots"
            if os.path.exists(screenshot_dir):
                files = os.listdir(screenshot_dir)
                image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

                if image_files:
                    # 选择最新的截图文件
                    latest_file = max(image_files, key=lambda x: os.path.getctime(os.path.join(screenshot_dir, x)))
                    file_path = os.path.join(screenshot_dir, latest_file)

                    # 读取图片并转换为base64
                    with open(file_path, 'rb') as f:
                        image_data = f.read()

                    # 添加到聊天上下文
                    from livekit.agents.llm import ImageContent

                    chat_ctx = self.chat_ctx.copy()
                    chat_ctx.add_message(
                        role="user",
                        content=[
                            f"请分析这张服务器截图的风水布局：{description}",
                            ImageContent(
                                image=f"data:image/png;base64,{base64.b64encode(image_data).decode('utf-8')}",
                                inference_detail="high"
                            )
                        ],
                    )

                    await self.update_chat_ctx(chat_ctx)
                    return f"✅ 已加载服务器截图 {latest_file} 进行风水分析"
                else:
                    return "服务器上暂无可用的截图文件，请上传图片或提供文字描述。"
            else:
                return "服务器截图目录不存在，请联系管理员配置。"

        except Exception as e:
            print(f"❌ 服务器截图分析失败: {e}")
            return f"服务器截图功能遇到问题：{e}，请尝试其他方式上传图片。"

async def entrypoint(ctx: JobContext):
    """Agent入口点 - 多模态版本"""
    await ctx.connect()
    print("🏮 启动多模态风水AI助手...")
    print(f"🎯 Agent会话开始: {ctx.room.name}")
    
    # 初始化AI组件
    stt = deepgram.STT(
        model="nova-2-general",
        language="zh",
        smart_format=True,
    )
    print("✅ Deepgram STT初始化成功")
    
    tts = cartesia.TTS(
        model="sonic-2",
        voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",
        language="zh",
        streaming=True,
        word_timestamps=True,
    )
    print("✅ Cartesia TTS初始化成功 (流式模式)")
    
    vad = silero.VAD.load(
        min_speech_duration_ms=100,
        min_silence_duration_ms=300,
        speech_pad_ms=50,
    )
    print("✅ Silero VAD初始化成功 (低延迟模式)")
    
    # DeepSeek LLM (支持Vision)
    from livekit.plugins import openai
    deepseek_llm = openai.LLM.with_deepseek(
        model="deepseek-chat",
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功 (支持Vision)")
    
    # 创建Agent会话
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,
        tts=tts,
        vad=vad,
        allow_interruptions=True,
        min_endpointing_delay=0.2,
        max_endpointing_delay=4.0,
        min_interruption_duration=0.3,
        preemptive_generation=True,
    )
    
    # 启动多模态Agent
    agent = MultimodalFengshuiAgent()
    await session.start(
        agent=agent,
        room=ctx.room,
    )
    
    print("🎯 多模态风水AI助手已就绪，支持图片分析功能")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
