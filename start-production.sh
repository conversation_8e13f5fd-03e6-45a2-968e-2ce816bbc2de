#!/bin/bash

# LiveKit风水AI助手 - 生产环境启动脚本
# 使用端口7000部署前端，避免与其他服务冲突

set -e

echo "🏮 启动LiveKit风水AI助手 - 生产环境"
echo "================================"

# 检查端口7000是否被占用
if lsof -Pi :7000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口7000已被占用，正在停止现有服务..."
    pkill -f "next.*7000" || true
    sleep 2
fi

# 启动前端服务 (端口7000)
echo "🎨 启动前端服务 (端口7000)..."
cd livekit-frontend

# 确保依赖已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端服务 (开发模式，跳过构建问题)
echo "🚀 启动前端服务 (开发模式)..."
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端启动
sleep 5

# 检查前端是否启动成功
if curl -s http://localhost:7000 > /dev/null; then
    echo "✅ 前端服务启动成功 (PID: $FRONTEND_PID)"
else
    echo "❌ 前端服务启动失败"
    exit 1
fi

# 返回根目录
cd ..

# 启动后端Agent
echo ""
echo "🔧 启动后端Agent..."
cd livekit-fengshui-agent

# 激活虚拟环境
source venv/bin/activate

# 启动Agent (后台运行) - 使用修复版Agent (支持图片上传)
echo "🚀 启动后端Agent (生产模式)..."
nohup python simple_agent_fixed.py start > ../logs/agent.log 2>&1 &
AGENT_PID=$!

# 等待Agent启动
sleep 3

echo "✅ 后端Agent启动成功 (PID: $AGENT_PID)"

# 返回根目录
cd ..

# 创建日志目录
mkdir -p logs

# 保存进程ID
echo $FRONTEND_PID > logs/frontend.pid
echo $AGENT_PID > logs/agent.pid

# 显示启动结果
echo ""
echo "🎉 LiveKit风水AI助手启动完成！"
echo "================================"
echo ""
echo "📊 服务状态:"
echo "前端服务: http://localhost:7000 (PID: $FRONTEND_PID)"
echo "后端Agent: 运行中 (PID: $AGENT_PID)"
echo "生产域名: https://su.guiyunai.fun"
echo ""
echo "📋 日志文件:"
echo "前端日志: logs/frontend.log"
echo "后端日志: logs/agent.log"
echo ""
echo "🛑 停止服务:"
echo "./stop-production.sh"
echo ""
echo "📊 查看状态:"
echo "./status.sh"
