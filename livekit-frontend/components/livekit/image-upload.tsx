'use client';

import React, { useCallback, useRef, useState } from 'react';
import { useRoomContext } from '@livekit/components-react';
import { ImageIcon, UploadIcon } from '@phosphor-icons/react/dist/ssr';
import { Button } from '@/components/ui/button';
import { toastAlert } from '@/components/alert-toast';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  className?: string;
  disabled?: boolean;
}

/**
 * 图片上传组件 - 基于LiveKit官方Byte Streams文档实现
 * 支持拖拽上传和点击选择文件
 */
export function ImageUpload({ className, disabled = false }: ImageUploadProps) {
  const room = useRoomContext();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileSelect = useCallback(
    async (file: File) => {
      if (!room || disabled) return;

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        toastAlert({
          title: '❌ 文件类型错误',
          description: '请选择图片文件（JPG、PNG、GIF等）',
          variant: 'destructive',
        });
        return;
      }

      // 验证文件大小 (限制为10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toastAlert({
          title: '❌ 文件过大',
          description: '图片文件大小不能超过10MB',
          variant: 'destructive',
        });
        return;
      }

      try {
        setIsUploading(true);

        // 使用LiveKit的sendFile方法上传图片
        // topic必须与Agent中注册的一致
        const info = await room.localParticipant.sendFile(file, {
          mimeType: file.type,
          topic: 'images', // 与Agent中的topic保持一致
          onProgress: (progress) => {
            console.log(`📤 上传进度: ${Math.ceil(progress * 100)}%`);
          },
        });

        toastAlert({
          title: '✅ 图片上传成功',
          description: '张大师正在分析您的图片，请稍候...',
          variant: 'default',
        });

        console.log('📷 图片上传成功，Stream ID:', info.id);
      } catch (error) {
        console.error('❌ 图片上传失败:', error);
        toastAlert({
          title: '❌ 上传失败',
          description: error instanceof Error ? error.message : '未知错误',
          variant: 'destructive',
        });
      } finally {
        setIsUploading(false);
      }
    },
    [room, disabled]
  );

  const handleButtonClick = useCallback(() => {
    if (disabled || isUploading) return;
    fileInputRef.current?.click();
  }, [disabled, isUploading]);

  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        handleFileSelect(file);
      }
      // 清空input值，允许重复选择同一文件
      event.target.value = '';
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && !isUploading) {
      setIsDragOver(true);
    }
  }, [disabled, isUploading]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      setIsDragOver(false);

      if (disabled || isUploading) return;

      const files = event.dataTransfer.files;
      if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
          handleFileSelect(file);
        } else {
          toastAlert({
            title: '❌ 文件类型错误',
            description: '请拖拽图片文件',
            variant: 'destructive',
          });
        }
      }
    },
    [disabled, isUploading, handleFileSelect]
  );

  const isButtonDisabled = disabled || isUploading || !room;

  return (
    <div className={cn('relative', className)}>
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={isButtonDisabled}
      />

      {/* 上传按钮 */}
      <Button
        variant="outline"
        size="sm"
        onClick={handleButtonClick}
        disabled={isButtonDisabled}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={cn(
          'flex items-center gap-2 transition-colors',
          isDragOver && 'border-blue-500 bg-blue-50 dark:bg-blue-950',
          isUploading && 'opacity-50 cursor-not-allowed'
        )}
        title={
          isButtonDisabled
            ? '请先连接到Agent'
            : '点击选择图片或拖拽图片到此处'
        }
      >
        {isUploading ? (
          <>
            <UploadIcon className="h-4 w-4 animate-spin" />
            <span className="text-sm">上传中...</span>
          </>
        ) : (
          <>
            <ImageIcon className="h-4 w-4" />
            <span className="text-sm">上传图片</span>
          </>
        )}
      </Button>

      {/* 拖拽提示 */}
      {isDragOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-md pointer-events-none">
          <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
            📷 释放以上传图片
          </div>
        </div>
      )}
    </div>
  );
}

export default ImageUpload;
