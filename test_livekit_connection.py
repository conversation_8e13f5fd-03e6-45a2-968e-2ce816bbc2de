#!/usr/bin/env python3
"""
LiveKit连接测试脚本
测试Agent是否能正确连接到LiveKit服务器
"""

import asyncio
import os
from dotenv import load_dotenv
from livekit import api

# 加载环境变量
load_dotenv("livekit-fengshui-agent/.env")

async def test_livekit_connection():
    """测试LiveKit连接"""
    print("🔍 测试LiveKit连接...")
    
    # 获取环境变量
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    livekit_url = os.getenv("LIVEKIT_URL")
    
    print(f"📊 配置信息:")
    print(f"  API Key: {api_key}")
    print(f"  API Secret: {api_secret[:10]}..." if api_secret else "  API Secret: None")
    print(f"  LiveKit URL: {livekit_url}")
    
    if not all([api_key, api_secret, livekit_url]):
        print("❌ 环境变量配置不完整")
        return False
    
    try:
        # 创建LiveKit API客户端
        lk_api = api.LiveKitAPI(
            url=livekit_url,
            api_key=api_key,
            api_secret=api_secret,
        )
        
        # 测试连接 - 列出房间
        print("🔗 尝试连接到LiveKit服务器...")
        rooms = await lk_api.room.list_rooms(api.ListRoomsRequest())
        
        print(f"✅ 连接成功！当前房间数量: {len(rooms.rooms)}")
        
        # 创建测试房间
        test_room_name = "test_connection_room"
        print(f"🏠 创建测试房间: {test_room_name}")
        
        room = await lk_api.room.create_room(api.CreateRoomRequest(
            name=test_room_name,
            empty_timeout=30,  # 30秒后自动删除空房间
        ))
        
        print(f"✅ 测试房间创建成功: {room.name}")
        
        # 清理测试房间
        await lk_api.room.delete_room(api.DeleteRoomRequest(room=test_room_name))
        print(f"🗑️ 测试房间已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_livekit_connection())
    if success:
        print("\n🎉 LiveKit连接测试通过！")
    else:
        print("\n💥 LiveKit连接测试失败！")
