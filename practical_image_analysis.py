#!/usr/bin/env python3
"""
实用的图片分析方案 - 基于测试结果的最佳实践
使用DeepSeek文本分析 + 图片描述生成的混合方案
"""

import os
import asyncio
import base64
import time
import requests
from dotenv import load_dotenv
from PIL import Image
import io

# 加载环境变量
load_dotenv('/www/wwwroot/su.guiyunai.fun/livekit-fengshui-agent/.env')

class PracticalFengshuiAnalyzer:
    """实用的风水图片分析器"""
    
    def __init__(self):
        self.deepseek_key = os.getenv("DEEPSEEK_API_KEY")
        self.groq_key = os.getenv("GROQ_API_KEY")  # Groq支持视觉模型
        
    async def analyze_image_with_description(self, image_bytes, user_description=""):
        """使用图片描述 + DeepSeek文本分析的方案"""
        print("🔍 开始图片分析...")
        
        # 方案1: 如果用户提供了描述，直接使用DeepSeek分析
        if user_description:
            return await self._analyze_with_deepseek(user_description)
        
        # 方案2: 尝试使用Groq视觉模型生成描述
        description = await self._generate_description_with_groq(image_bytes)
        if description:
            print(f"📝 图片描述: {description}")
            return await self._analyze_with_deepseek(description)
        
        # 方案3: 使用基础图片分析
        return await self._basic_image_analysis(image_bytes)
    
    async def _analyze_with_deepseek(self, description):
        """使用DeepSeek分析房间描述"""
        try:
            headers = {
                'Authorization': f'Bearer {self.deepseek_key}',
                'Content-Type': 'application/json'
            }
            
            prompt = f"""请作为专业风水大师分析以下房间情况：

房间描述：{description}

请提供：
1. 🏠 布局分析：评价当前布局的风水优缺点
2. ⚠️ 问题识别：指出可能的风水问题
3. 💡 改善建议：提供3-5个具体的改善方案
4. 🎨 装饰建议：推荐合适的颜色和装饰

请用专业但通俗易懂的语言，控制在300字内。"""
            
            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 600
            }
            
            start_time = time.time()
            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                analysis = result['choices'][0]['message']['content']
                
                return {
                    'success': True,
                    'analysis': analysis,
                    'response_time': end_time - start_time,
                    'method': 'DeepSeek文本分析',
                    'cost_estimate': self._calculate_cost(result.get('usage', {}))
                }
            else:
                return {
                    'success': False,
                    'error': f"DeepSeek API错误: {response.status_code}",
                    'method': 'DeepSeek文本分析'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'method': 'DeepSeek文本分析'
            }
    
    async def _generate_description_with_groq(self, image_bytes):
        """使用Groq生成图片描述"""
        if not self.groq_key:
            return None
            
        try:
            # Groq的视觉模型API调用
            image_base64 = base64.b64encode(image_bytes).decode('utf-8')
            
            headers = {
                'Authorization': f'Bearer {self.groq_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "model": "llava-v1.5-7b-4096-preview",  # Groq的视觉模型
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "请详细描述这张房间图片，包括：房间类型、家具摆放、颜色搭配、整体布局。用中文回答。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 300
            }
            
            response = requests.post(
                "https://api.groq.com/openai/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                description = result['choices'][0]['message']['content']
                print("✅ Groq图片描述生成成功")
                return description
            else:
                print(f"❌ Groq API错误: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Groq描述生成失败: {e}")
            return None
    
    async def _basic_image_analysis(self, image_bytes):
        """基础图片分析（不依赖视觉AI）"""
        try:
            # 分析图片基本信息
            image = Image.open(io.BytesIO(image_bytes))
            width, height = image.size
            
            # 分析主要颜色
            colors = image.getcolors(maxcolors=256*256*256)
            if colors:
                # 获取最主要的颜色
                dominant_color = max(colors, key=lambda x: x[0])
                color_rgb = dominant_color[1]
                
                # 简单的颜色分类
                if sum(color_rgb) > 600:  # 偏亮色
                    color_desc = "整体色调偏亮，有利于阳气聚集"
                elif sum(color_rgb) < 300:  # 偏暗色
                    color_desc = "整体色调偏暗，建议增加明亮元素"
                else:
                    color_desc = "色调适中，符合阴阳平衡"
            else:
                color_desc = "色调分析不可用"
            
            # 基于图片尺寸的布局建议
            if width > height:
                layout_desc = "横向布局，适合客厅或办公空间"
            else:
                layout_desc = "纵向布局，适合卧室或书房"
            
            # 生成基础分析
            basic_analysis = f"""
🏠 **基础图片分析**

📐 **尺寸信息**：{width}x{height}像素，{layout_desc}

🎨 **色彩分析**：{color_desc}

💡 **通用建议**：
1. 确保房间采光充足，避免阴暗角落
2. 保持空间整洁，物品摆放有序
3. 适当添加绿植，改善室内气场
4. 注意家具摆放，避免尖角对人
5. 选择温暖色调的装饰，营造和谐氛围

⚠️ **提示**：如果您能描述一下房间的具体情况（如家具摆放、房间用途等），我可以提供更精确的风水分析。
            """
            
            return {
                'success': True,
                'analysis': basic_analysis,
                'method': '基础图片分析',
                'response_time': 0.1,
                'cost_estimate': {'total_cost_cny': 0}
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'method': '基础图片分析'
            }
    
    def _calculate_cost(self, usage):
        """计算DeepSeek成本"""
        input_tokens = usage.get('prompt_tokens', 0)
        output_tokens = usage.get('completion_tokens', 0)
        
        # DeepSeek价格: 输入$0.14/1M tokens, 输出$0.28/1M tokens
        input_cost = (input_tokens / 1000000) * 0.14
        output_cost = (output_tokens / 1000000) * 0.28
        total_cost = input_cost + output_cost
        
        return {
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'total_cost_usd': round(total_cost, 6),
            'total_cost_cny': round(total_cost * 7.2, 4)
        }

async def test_practical_analyzer():
    """测试实用分析器"""
    print("🏮 测试实用风水图片分析器")
    print("="*50)
    
    analyzer = PracticalFengshuiAnalyzer()
    
    # 创建测试图片
    from PIL import ImageDraw
    img = Image.new('RGB', (400, 300), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # 画一个简单的客厅
    draw.rectangle([50, 50, 350, 250], outline='black', width=3)
    draw.rectangle([80, 80, 150, 120], fill='brown', outline='black')  # 茶几
    draw.rectangle([200, 180, 320, 220], fill='darkblue', outline='black')  # 沙发
    draw.rectangle([320, 60, 340, 100], fill='green', outline='black')  # 植物
    
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    image_bytes = buffer.getvalue()
    
    print(f"📷 创建测试图片 (大小: {len(image_bytes)} bytes)")
    
    # 测试1: 带用户描述的分析
    print("\n🧪 测试1: 用户描述 + DeepSeek分析")
    user_desc = "这是一个现代风格的客厅，有一个棕色茶几、深蓝色沙发和一盆绿植。沙发靠近窗户，茶几在房间中央。"
    result1 = await analyzer.analyze_image_with_description(image_bytes, user_desc)
    
    if result1['success']:
        print(f"✅ 分析成功 ({result1['method']})")
        print(f"⏱️ 响应时间: {result1['response_time']:.2f}秒")
        print(f"💰 成本: ¥{result1['cost_estimate']['total_cost_cny']}")
        print(f"📝 分析结果:\n{result1['analysis']}")
    else:
        print(f"❌ 分析失败: {result1['error']}")
    
    # 测试2: 纯图片分析
    print("\n🧪 测试2: 纯图片分析")
    result2 = await analyzer.analyze_image_with_description(image_bytes)
    
    if result2['success']:
        print(f"✅ 分析成功 ({result2['method']})")
        print(f"⏱️ 响应时间: {result2['response_time']:.2f}秒")
        print(f"💰 成本: ¥{result2['cost_estimate']['total_cost_cny']}")
        print(f"📝 分析结果:\n{result2['analysis']}")
    else:
        print(f"❌ 分析失败: {result2['error']}")
    
    # 总结
    print("\n" + "="*50)
    print("🎯 实用方案总结:")
    print("1. ✅ DeepSeek文本分析：专业、快速、成本低")
    print("2. 🔄 Groq视觉描述：可选的图片理解能力")
    print("3. 📊 基础分析：无AI依赖的备用方案")
    print("4. 💡 推荐：用户描述 + DeepSeek分析 = 最佳性价比")

if __name__ == "__main__":
    asyncio.run(test_practical_analyzer())
