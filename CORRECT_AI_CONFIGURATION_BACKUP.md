# ✅ 正确的AI配置备份文档

**创建时间**: 2025-08-04  
**状态**: ✅ 已验证正常工作  
**用途**: 风水AI助手语音对话系统  

## 🎯 正确的AI组件配置

### 核心配置组合
- ✅ **STT**: Deepgram Nova-2 (中文语音识别)
- ✅ **LLM**: DeepSeek Chat (对话生成)
- ✅ **TTS**: Cartesia Sonic-2 (语音合成)
- ✅ **VAD**: <PERSON><PERSON><PERSON> (语音活动检测)

## 🔧 关键配置文件

### 1. 主Agent文件: `simple_agent_fixed.py`
```python
# 正确的导入顺序
from livekit.plugins import deepgram, cartesia, silero

async def entrypoint(ctx: JobContext):
    await ctx.connect()  # 关键：必须先连接
    
    # STT - Deepgram
    stt = deepgram.STT(
        model="nova-2-general",
        language="zh",
        smart_format=True,
    )
    
    # TTS - Cartesia
    tts = cartesia.TTS(
        model="sonic-2",
        voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 中文女声
        language="zh",
    )
    
    # VAD - Silero
    vad = silero.VAD.load()
    
    # LLM - DeepSeek (在函数内导入)
    from livekit.plugins import openai
    deepseek_llm = openai.LLM.with_deepseek(
        model="deepseek-chat",
        temperature=0.7
    )
    
    # 创建会话
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,
        tts=tts,
        vad=vad,
    )
    
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )

# 使用自动分发模式
cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
```

### 2. 环境变量配置: `.env`
```bash
# 正确的API密钥配置
DEEPSEEK_API_KEY=***********************************
DEEPGRAM_API_KEY=****************************************
CARTESIA_API_KEY=sk_car_M4xSDprdaeVW6Q9MRqSGfw
LIVEKIT_API_KEY=API7Na43BYsXGmi
LIVEKIT_API_SECRET=HlXvb2pbK6w5btYz8azIqfKvhPbfBck8rbJbKYIfDoMB
LIVEKIT_URL=wss://kjh-a5mlk6sq.livekit.cloud

# 已删除的冲突配置
# OPENAI_API_KEY=removed-to-avoid-conflicts
# OPENAI_BASE_URL=removed-to-avoid-conflicts
```

## ❌ 已删除的错误配置

### 删除的文件
- `agent_clean.py` - 包含错误的OpenAI TTS配置

### 修复的问题
1. **OpenAI TTS冲突**: 删除了使用DeepSeek密钥调用OpenAI TTS的错误配置
2. **环境变量冲突**: 清理了OPENAI_API_KEY和OPENAI_BASE_URL的错误设置
3. **导入顺序**: 修复了openai插件的导入时机
4. **Agent分发**: 移除了agent_name限制，使用自动分发模式

## 🧪 验证测试

### 成功的测试日志
```
🏮 启动风水AI助手...
🎯 Agent会话开始: test-room
✅ Deepgram STT初始化成功
✅ Cartesia TTS初始化成功
✅ Silero VAD初始化成功
✅ DeepSeek LLM初始化成功
🎯 风水AI助手已就绪，等待用户交互
```

### LiveKit连接状态
```
✅ Worker注册成功
✅ 连接到新加坡区域
✅ 协议版本: 16
✅ 音频IO管道正常建立
✅ 语音对话功能正常
```

## 🚨 重要提醒

### 不要恢复的配置
- 任何包含 `openai.TTS` 的配置
- 任何将 `OPENAI_API_KEY` 设置为DeepSeek密钥的配置
- 任何在文件顶部导入openai插件的配置

### 保持的关键点
1. **await ctx.connect()** - 必须在entrypoint开始时调用
2. **Cartesia TTS** - 使用正确的中文女声ID
3. **函数内导入** - openai插件在需要时才导入
4. **自动分发** - 不设置agent_name，让任何连接都能触发

## 📞 故障排除

如果语音功能出现问题，检查：
1. 环境变量是否包含OPENAI相关配置（应该没有）
2. 是否有文件使用openai.TTS（应该没有）
3. Agent日志是否显示所有组件初始化成功
4. LiveKit连接是否正常建立

**🎯 此配置已验证可以正常进行中文语音对话！**
