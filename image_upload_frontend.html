<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏮 风水AI助手 - 图片分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #fff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .title {
            font-size: 28px;
            color: #d4af37;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 16px;
            color: #ccc;
        }

        .upload-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .upload-area {
            border: 3px dashed #d4af37;
            border-radius: 15px;
            padding: 60px 20px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #ffd700;
            background: rgba(212, 175, 55, 0.1);
        }

        .upload-area.dragover {
            border-color: #ffd700;
            background: rgba(212, 175, 55, 0.2);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 64px;
            color: #d4af37;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 18px;
            color: #fff;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 14px;
            color: #ccc;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #d4af37, #ffd700);
            color: #8b0000;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
        }

        .upload-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .preview-container {
            margin-top: 30px;
            display: none;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .analysis-result {
            margin-top: 30px;
            padding: 20px;
            background: rgba(212, 175, 55, 0.1);
            border-radius: 10px;
            border-left: 4px solid #d4af37;
            display: none;
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .loading-spinner {
            border: 3px solid rgba(212, 175, 55, 0.3);
            border-top: 3px solid #d4af37;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
            max-width: 800px;
            width: 100%;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 16px;
            color: #d4af37;
            margin-bottom: 8px;
        }

        .feature-desc {
            font-size: 14px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🏮</div>
        <h1 class="title">风水AI助手</h1>
        <p class="subtitle">上传房屋图片，获得专业风水分析</p>
    </div>

    <div class="upload-container">
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📷</div>
            <div class="upload-text">点击或拖拽上传图片</div>
            <div class="upload-hint">支持 JPG、PNG、JPEG 格式，最大 10MB</div>
        </div>

        <input type="file" id="fileInput" class="file-input" accept="image/*">
        <button class="upload-btn" id="uploadBtn">选择图片</button>

        <div class="preview-container" id="previewContainer">
            <img id="previewImage" class="preview-image" alt="预览图片">
        </div>

        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>张大师正在分析中...</div>
        </div>

        <div class="analysis-result" id="analysisResult">
            <h3>🔍 风水分析结果</h3>
            <div id="resultText"></div>
        </div>
    </div>

    <div class="features">
        <div class="feature-card">
            <div class="feature-icon">🏠</div>
            <div class="feature-title">住宅风水</div>
            <div class="feature-desc">分析房屋布局、朝向、装修风水</div>
        </div>
        <div class="feature-card">
            <div class="feature-icon">🏢</div>
            <div class="feature-title">商业风水</div>
            <div class="feature-desc">办公室、店铺、工厂风水布局</div>
        </div>
        <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <div class="feature-title">装饰风水</div>
            <div class="feature-desc">颜色搭配、摆设位置建议</div>
        </div>
        <div class="feature-card">
            <div class="feature-icon">🧭</div>
            <div class="feature-title">方位分析</div>
            <div class="feature-desc">八卦方位、五行配置指导</div>
        </div>
    </div>

    <script>
        class FengshuiImageAnalyzer {
            constructor() {
                this.initializeElements();
                this.bindEvents();
            }

            initializeElements() {
                this.uploadArea = document.getElementById('uploadArea');
                this.fileInput = document.getElementById('fileInput');
                this.uploadBtn = document.getElementById('uploadBtn');
                this.previewContainer = document.getElementById('previewContainer');
                this.previewImage = document.getElementById('previewImage');
                this.loading = document.getElementById('loading');
                this.analysisResult = document.getElementById('analysisResult');
                this.resultText = document.getElementById('resultText');
            }

            bindEvents() {
                // 点击上传区域
                this.uploadArea.addEventListener('click', () => {
                    this.fileInput.click();
                });

                // 点击上传按钮
                this.uploadBtn.addEventListener('click', () => {
                    this.fileInput.click();
                });

                // 文件选择
                this.fileInput.addEventListener('change', (e) => {
                    this.handleFileSelect(e.target.files[0]);
                });

                // 拖拽上传
                this.uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.uploadArea.classList.add('dragover');
                });

                this.uploadArea.addEventListener('dragleave', () => {
                    this.uploadArea.classList.remove('dragover');
                });

                this.uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.uploadArea.classList.remove('dragover');
                    const file = e.dataTransfer.files[0];
                    if (file && file.type.startsWith('image/')) {
                        this.handleFileSelect(file);
                    }
                });
            }

            handleFileSelect(file) {
                if (!file) return;

                // 验证文件类型
                if (!file.type.startsWith('image/')) {
                    alert('请选择图片文件！');
                    return;
                }

                // 验证文件大小 (10MB)
                if (file.size > 10 * 1024 * 1024) {
                    alert('图片大小不能超过10MB！');
                    return;
                }

                // 显示预览
                this.showPreview(file);

                // 开始分析
                this.analyzeImage(file);
            }

            showPreview(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.previewImage.src = e.target.result;
                    this.previewContainer.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }

            async analyzeImage(file) {
                // 显示加载状态
                this.loading.style.display = 'block';
                this.analysisResult.style.display = 'none';

                try {
                    // 模拟API调用 - 实际应该连接到LiveKit Agent
                    const result = await this.callFengshuiAPI(file);
                    
                    // 显示结果
                    this.showResult(result);
                } catch (error) {
                    console.error('分析失败:', error);
                    this.showResult('分析过程中出现错误，请稍后重试。');
                } finally {
                    this.loading.style.display = 'none';
                }
            }

            async callFengshuiAPI(file) {
                // 这里应该连接到LiveKit Agent
                // 目前返回模拟结果
                return new Promise((resolve) => {
                    setTimeout(() => {
                        resolve(`
                            <strong>🏠 房间布局分析：</strong><br>
                            从图片看，这是一个现代风格的客厅，整体布局较为合理。<br><br>
                            
                            <strong>🎨 颜色搭配：</strong><br>
                            主色调以暖色系为主，符合风水中的温暖原则。<br><br>
                            
                            <strong>⚠️ 风水建议：</strong><br>
                            1. 建议在东南角放置绿植，有助于财运<br>
                            2. 沙发背后最好有实墙，增加安全感<br>
                            3. 可以添加一些金色装饰，提升贵气<br><br>
                            
                            <strong>📈 改善方案：</strong><br>
                            整体风水良好，小幅调整即可达到更佳效果。
                        `);
                    }, 3000);
                });
            }

            showResult(result) {
                this.resultText.innerHTML = result;
                this.analysisResult.style.display = 'block';
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new FengshuiImageAnalyzer();
        });
    </script>
</body>
</html>
