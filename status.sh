#!/bin/bash

# LiveKit风水AI助手 - 服务状态检查脚本

echo "📊 LiveKit风水AI助手服务状态"
echo "================================"

# 检查前端服务
echo "🎨 前端服务状态:"
if curl -s http://localhost:7000 > /dev/null; then
    echo "✅ 前端服务运行正常 (端口7000)"
    if [ -f "logs/frontend.pid" ]; then
        FRONTEND_PID=$(cat logs/frontend.pid)
        echo "   PID: $FRONTEND_PID"
    fi
else
    echo "❌ 前端服务未运行"
fi

echo ""

# 检查后端Agent
echo "🔧 后端Agent状态:"
if [ -f "logs/agent.pid" ]; then
    AGENT_PID=$(cat logs/agent.pid)
    if kill -0 $AGENT_PID 2>/dev/null; then
        echo "✅ 后端Agent运行正常 (PID: $AGENT_PID)"
    else
        echo "❌ 后端Agent未运行"
    fi
else
    echo "❌ 后端Agent未运行"
fi

echo ""

# 检查端口占用
echo "🌐 端口占用情况:"
if command -v ss >/dev/null 2>&1; then
    ss -tlnp | grep -E ":7000|:8080" || echo "   端口7000和8080未被占用"
elif command -v netstat >/dev/null 2>&1; then
    netstat -tlnp | grep -E ":7000|:8080" || echo "   端口7000和8080未被占用"
else
    echo "   无法检查端口状态 (需要安装ss或netstat)"
fi

echo ""

# 检查域名访问
echo "🌍 域名访问状态:"
if curl -s -I https://su.guiyunai.fun | head -1 | grep -q "200\|301\|302"; then
    echo "✅ 域名 https://su.guiyunai.fun 可访问"
else
    echo "❌ 域名 https://su.guiyunai.fun 无法访问"
fi

echo ""

# 显示最近的日志
echo "📋 最近日志 (最后10行):"
if [ -f "logs/frontend.log" ]; then
    echo "--- 前端日志 ---"
    tail -5 logs/frontend.log
fi

if [ -f "logs/agent.log" ]; then
    echo "--- 后端日志 ---"
    tail -5 logs/agent.log
fi
