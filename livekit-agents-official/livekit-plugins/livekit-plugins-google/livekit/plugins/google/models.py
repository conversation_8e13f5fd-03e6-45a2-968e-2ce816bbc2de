from typing import Literal

# Speech to Text v2

SpeechModels = Literal[
    "long",
    "short",
    "telephony",
    "medical_dictation",
    "medical_conversation",
    "chirp",
    "chirp_2",
    "latest_long",
    "latest_short",
]

SpeechLanguages = Literal[
    "en-US",
    "ja-JP",
    "en-IN",
    "en-GB",
    "hi-IN",
    "af-ZA",
    "sq-AL",
    "am-ET",
    "ar-EG",
    "hy-AM",
    "ast-ES",
    "az-AZ",
    "eu-ES",
    "be-BY",
    "bs-BA",
    "bg-BG",
    "my-MM",
    "ca-ES",
    "ceb-PH",
    "ckb-IQ",
    "zh-Hans-CN",
    "yue-Hant-HK",
    "zh-TW",
    "hr-HR",
    "cs-CZ",
    "da-DK",
    "nl-NL",
    "en-AU",
    "et-EE",
    "fil-PH",
    "fi-FI",
    "fr-CA",
    "fr-FR",
    "gl-ES",
    "ka-GE",
    "de-DE",
    "el-GR",
    "gu-IN",
    "ha-NG",
    "iw-IL",
    "hi-IN",
    "hu-HU",
    "is-IS",
    "id-ID",
    "it-IT",
    "ja-JP",
    "jv-ID",
    "kea-CV",
    "kam-KE",
    "kn-IN",
    "kk-KZ",
    "km-KH",
    "ko-KR",
    "ky-KG",
    "lo-LA",
    "lv-LV",
    "ln-CD",
    "lt-LT",
    "luo-KE",
    "lb-LU",
    "mk-MK",
    "no-NO",
    "pl-PL",
    "pt-BR",
    "pt-PT",
    "ro-RO",
    "ru-RU",
    "es-CO",
    "es-MX",
    "es-US",
    "th-TH",
    "tr-TR",
    "uk-UA",
    "vi-VN",
    "da-DK",
]

Gender = Literal["male", "female", "neutral"]

ChatModels = Literal[
    "gemini-2.5-pro-preview-05-06",
    "gemini-2.5-flash-preview-04-17",
    "gemini-2.5-flash-preview-05-20",
    "gemini-2.0-flash-001",
    "gemini-2.0-flash-lite-preview-02-05",
    "gemini-2.0-pro-exp-02-05",
    "gemini-1.5-pro",
]
