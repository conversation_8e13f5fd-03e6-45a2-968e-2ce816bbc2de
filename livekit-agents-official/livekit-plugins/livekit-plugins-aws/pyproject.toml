[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "livekit-plugins-aws"
dynamic = ["version"]
description = "LiveKit Agents Plugin for services from AWS"
readme = "README.md"
license = "Apache-2.0"
requires-python = ">=3.9.0"
authors = [{ name = "LiveKit", email = "<EMAIL>" }]
keywords = ["webrtc", "realtime", "audio", "video", "livekit", "aws", "nova", "sonic"]
classifiers = [
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Multimedia :: Video",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3 :: Only",
]
dependencies = [
    "livekit-agents>=1.2.2",
    "aioboto3>=14.1.0",
    "amazon-transcribe>=0.6.2",
]

[project.optional-dependencies]
realtime = [
    "aws-sdk-bedrock-runtime==0.0.2; python_version >= '3.12'",
    "boto3>1.35.10",
]

[project.urls]
Documentation = "https://docs.livekit.io"
Website = "https://livekit.io/"
Source = "https://github.com/livekit/agents"

[tool.hatch.version]
path = "livekit/plugins/aws/version.py"

[tool.hatch.build.targets.wheel]
packages = ["livekit"]

[tool.hatch.build.targets.sdist]
include = ["/livekit"]
