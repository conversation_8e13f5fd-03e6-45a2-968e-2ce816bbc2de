# Clova plugin for LiveKit Agents

Support for speech-to-text with [<PERSON><PERSON><PERSON>](https://api.ncloud-docs.com/docs/).

See https://docs.livekit.io/agents/integrations/stt/clova/ for more information.

## Installation

```bash
pip install livekit-plugins-clova
```

## Pre-requisites

You need invoke url and secret key from Naver cloud platform -> <PERSON><PERSON><PERSON> Speech and set as environment variables: `CLOVA_STT_INVOKE_URL` & `CLOVA_STT_SECRET_KEY`
