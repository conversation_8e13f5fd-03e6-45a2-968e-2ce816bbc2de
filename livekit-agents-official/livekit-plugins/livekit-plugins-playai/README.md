# PlayAI/PlayHT plugin for LiveKit Agents

Support for voice synthesis with [PlayAI](https://play.ai/).

See [https://docs.livekit.io/agents/integrations/tts/playai/](https://docs.livekit.io/agents/integrations/tts/playai/) for more information.

## Installation

```bash
pip install livekit-plugins-playai
```

## Pre-requisites

You'll need USER ID and API Secret KEY from PlayHT. It can be set as an environment variable: `PLAYHT_USER_ID`, `PLAYHT_API_KEY` get it from [here](https://play.ht/studio/api-access)
