# Cartesia plugin for LiveKit Agents

Support for [Cartesia](https://cartesia.ai/)'s voice AI services in LiveKit Agents.

More information is available in the docs for the [STT](https://docs.livekit.io/agents/integrations/stt/cartesia/) and [TTS](https://docs.livekit.io/agents/integrations/tts/cartesia/) integrations.

## Installation

```bash
pip install livekit-plugins-cartesia
```

## Pre-requisites

You'll need an API key from Cartesia. It can be set as an environment variable: `CARTESIA_API_KEY`
