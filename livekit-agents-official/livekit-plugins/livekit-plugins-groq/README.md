# Groq plugin for LiveKit Agents

Support for STT, TTS, and LLM with [Groq](https://www.groq.com/) fast inference.

See [https://docs.livekit.io/agents/integrations/groq/](https://docs.livekit.io/agents/integrations/groq/) for more information.

## Installation

```bash
pip install livekit-plugins-groq
```

## Pre-requisites

For credentials, you'll need a Groq Cloud account and obtain the correct credentials. Credentials can be passed directly or via GROQ_API_KEY environment variable
