from typing import Literal

# listing production models from https://console.groq.com/docs/models

STTModels = Literal[
    "whisper-large-v3",
    "whisper-large-v3-turbo",
    "distil-whisper-large-v3-en",
]

LLMModels = Literal[
    "llama3-8b-8192",
    "llama3-70b-8192",
    "llama-guard-3-8b",
    "llama-3.1-8b-instant",
    "llama-3.3-70b-versatile",
    "meta-llama/llama-4-scout-17b-16e-instruct",
    "meta-llama/llama-4-maverick-17b-128e-instruct",
    "deepseek-r1-distill-llama-70b",
]

TTSModels = Literal[
    "playai-tts",
    "playai-tts-arabic",
]

TTSVoices = Literal[
    # english voices
    "Arista-PlayAI",
    "Atlas-PlayAI",
    "Basil-PlayAI",
    "Briggs-PlayAI",
    "Calum-PlayAI",
    "Celeste-PlayA<PERSON>",
    "Cheyenne-PlayA<PERSON>",
    "Chip-<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>",
    "Fritz-PlayA<PERSON>",
    "Gail-<PERSON>A<PERSON>",
    "Indigo-PlayAI",
    "Mamaw-PlayA<PERSON>",
    "Mason-PlayA<PERSON>",
    "<PERSON><PERSON>l-<PERSON><PERSON><PERSON>",
    "<PERSON>-Play<PERSON><PERSON>",
    "<PERSON>-Play<PERSON><PERSON>",
    "Thunder-PlayA<PERSON>",
    # arabic voices
    "Na<PERSON>-Play<PERSON><PERSON>",
    "<PERSON>-PlayA<PERSON>",
    "<PERSON>a-Play<PERSON><PERSON>",
    "<PERSON>-Play<PERSON><PERSON>",
]
