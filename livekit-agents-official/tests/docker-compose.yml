services:
  toxiproxy:
    build:
      context: ..
      dockerfile: tests/Dockerfile.toxiproxy
    command: ["toxiproxy-server", "-host", "0.0.0.0", "-port", "8474"]
    environment:
      - LOG_LEVEL=info
    # ports:
    #   - "8474:8474"
    #   - "443:443"
    networks:
      toxinet:
        ipv4_address: ***********

  app:
    build:
      context: ..
      dockerfile: tests/Dockerfile.tests
    command: tail -f /dev/null
    volumes:
      - ../tests:/app/tests
      - ../livekit-agents:/app/livekit-agents
      - ../livekit-plugins:/app/livekit-plugins
      - ../pyproject.toml:/app/pyproject.toml
      - ../uv.lock:/app/uv.lock
      - ../lk_dump:/app/lk_dump
    environment:
      # debug
      - LK_DUMP_TTS=1
      - LK_OPENAI_DEBUG=1

      - LIVEKIT_URL
      - LIVEKIT_API_KEY
      - LIVEKIT_API_SECRET
      - DEEPGRAM_API_KEY
      - OPENAI_API_KEY
      - ELEVEN_API_KEY
      - CARTESIA_API_KEY
      - AZURE_SPEECH_KEY
      - AZURE_SPEECH_REGION
      - GOOGLE_CREDENTIALS_JSON
      - ANTHROPIC_API_KEY
      - GROQ_API_KEY
      - ASSEMBLYAI_API_KEY
      - FAL_KEY
      - PLAYHT_API_KEY
      - PLAYHT_USER_ID
      - GOOGLE_API_KEY
      - RIME_API_KEY
      - SPEECHMATICS_API_KEY
      - GOOGLE_APPLICATION_CREDENTIALS
      - AWS_ACCESS_KEY_ID
      - AWS_SECRET_ACCESS_KEY
      - NEUPHONIC_API_KEY
      - RESEMBLE_API_KEY
      - SPEECHIFY_API_KEY
      - HUME_API_KEY
      - SPITCH_API_KEY
      - LMNT_API_KEY
      - INWORLD_API_KEY
      - MISTRAL_API_KEY

    extra_hosts:
      - "polly.us-west-2.amazonaws.com:***********"
      - "westus.tts.speech.microsoft.com:***********"
      - "api.cartesia.ai:***********"
      - "api.deepgram.com:***********"
      - "api.elevenlabs.io:***********"
      - "api.sws.speechify.com:***********"
      - "texttospeech.googleapis.com:***********"
      - "api.groq.com:***********"
      - "api.neuphonic.com:***********"
      - "api.openai.com:***********"
      - "api.play.ht:***********"
      - "f.cluster.resemble.ai:***********"
      - "websocket.cluster.resemble.ai:***********"
      - "users.rime.ai:***********"
      - "api.hume.ai:***********"
      - "api.lmnt.com:***********"
      - "api.inworld.ai:***********"
      - "api.mistralai.com:***********"
    networks:
      - toxinet

networks:
  toxinet:
    driver: bridge
    ipam:
      config:
        - subnet: "**********/16"
