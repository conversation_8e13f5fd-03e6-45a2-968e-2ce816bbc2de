#!/usr/bin/env python3
"""
测试LiveKit风水AI助手的多模态图片分析功能
"""

import os
import asyncio
import base64
from dotenv import load_dotenv

load_dotenv()

async def test_deepseek_vision_api():
    """测试DeepSeek Vision API"""
    print("🧪 测试DeepSeek Vision API...")
    
    try:
        import requests
        
        # 创建一个简单的测试图片 (1x1像素的PNG)
        test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="
        
        headers = {
            'Authorization': f'Bearer {os.getenv("DEEPSEEK_API_KEY")}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": "deepseek-vl-chat",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请描述这张图片"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{test_image_base64}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        response = requests.post(
            'https://api.deepseek.com/v1/chat/completions',
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            analysis = result['choices'][0]['message']['content']
            print(f"✅ DeepSeek Vision API测试成功")
            print(f"📋 响应: {analysis}")
            return True
        else:
            print(f"❌ DeepSeek API错误: {response.status_code}")
            print(f"📋 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek Vision API测试失败: {e}")
        return False

def test_knowledge_images_directory():
    """测试知识库图片目录"""
    print("\n🧪 测试知识库图片目录...")
    
    try:
        images_dir = os.path.join(os.path.dirname(__file__), 'knowledge_images')
        
        if not os.path.exists(images_dir):
            os.makedirs(images_dir)
            print(f"✅ 创建知识库图片目录: {images_dir}")
        else:
            print(f"✅ 知识库图片目录已存在: {images_dir}")
        
        # 检查目录中的图片文件
        image_files = []
        for filename in os.listdir(images_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_files.append(filename)
        
        if image_files:
            print(f"📷 找到 {len(image_files)} 张图片:")
            for filename in image_files:
                print(f"   - {filename}")
        else:
            print("📷 目录中暂无图片文件")
            # 创建一个说明文件
            readme_path = os.path.join(images_dir, "README.txt")
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write("""风水知识库图片目录

请将参考图片放入此目录，支持的格式：
- PNG (.png)
- JPEG (.jpg, .jpeg)

文件命名建议：
- 客厅_现代风格.jpg
- 办公室_中式布局.png
- 卧室_简约设计.jpeg

这样可以通过关键词搜索到相关图片。
""")
            print(f"✅ 已创建说明文件: {readme_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 知识库图片目录测试失败: {e}")
        return False

def test_server_screenshots_directory():
    """测试服务器截图目录"""
    print("\n🧪 测试服务器截图目录...")
    
    try:
        screenshot_dir = "/www/wwwroot/su.guiyunai.fun/screenshots"
        
        if not os.path.exists(screenshot_dir):
            os.makedirs(screenshot_dir)
            print(f"✅ 创建服务器截图目录: {screenshot_dir}")
        else:
            print(f"✅ 服务器截图目录已存在: {screenshot_dir}")
        
        # 检查目录权限
        if os.access(screenshot_dir, os.W_OK):
            print("✅ 目录可写")
        else:
            print("⚠️ 目录不可写，可能需要调整权限")
        
        # 检查现有截图文件
        image_files = []
        for filename in os.listdir(screenshot_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                file_path = os.path.join(screenshot_dir, filename)
                image_files.append((filename, os.path.getctime(file_path)))
        
        if image_files:
            image_files.sort(key=lambda x: x[1], reverse=True)
            print(f"📷 找到 {len(image_files)} 张截图:")
            for filename, ctime in image_files[:5]:  # 只显示最新的5张
                import time
                time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ctime))
                print(f"   - {filename} ({time_str})")
        else:
            print("📷 目录中暂无截图文件")
            # 创建说明文件
            readme_path = os.path.join(screenshot_dir, "README.txt")
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write("""服务器截图目录

此目录用于存放需要进行风水分析的截图文件。

支持的格式：
- PNG (.png)
- JPEG (.jpg, .jpeg)

使用方法：
1. 将截图文件放入此目录
2. 通过Agent的analyze_server_screenshot工具进行分析
3. 系统会自动选择最新的截图进行分析

注意：
- 文件会按创建时间排序
- 建议定期清理旧文件
""")
            print(f"✅ 已创建说明文件: {readme_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务器截图目录测试失败: {e}")
        return False

async def test_unsplash_api():
    """测试Unsplash API"""
    print("\n🧪 测试Unsplash图片搜索API...")
    
    try:
        import requests
        
        # 使用demo key测试
        search_url = "https://api.unsplash.com/search/photos"
        params = {
            'query': 'living room interior design',
            'per_page': 2,
            'client_id': os.getenv('UNSPLASH_ACCESS_KEY', 'demo')
        }
        
        response = requests.get(search_url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            images = data.get('results', [])
            
            if images:
                print(f"✅ Unsplash API测试成功，找到 {len(images)} 张图片:")
                for i, img in enumerate(images, 1):
                    print(f"   {i}. {img.get('description', '无描述')}")
                    print(f"      URL: {img['urls']['small']}")
                return True
            else:
                print("⚠️ API响应正常但未找到图片")
                return False
        else:
            print(f"❌ Unsplash API错误: {response.status_code}")
            if response.status_code == 401:
                print("💡 提示: 需要设置UNSPLASH_ACCESS_KEY环境变量")
            return False
            
    except Exception as e:
        print(f"❌ Unsplash API测试失败: {e}")
        return False

def test_environment_variables():
    """测试环境变量配置"""
    print("\n🧪 测试环境变量配置...")
    
    required_vars = [
        'DEEPSEEK_API_KEY',
        'LIVEKIT_URL',
        'LIVEKIT_API_KEY',
        'LIVEKIT_API_SECRET',
        'DEEPGRAM_API_KEY',
        'CARTESIA_API_KEY'
    ]
    
    optional_vars = [
        'UNSPLASH_ACCESS_KEY'
    ]
    
    all_good = True
    
    print("📋 必需的环境变量:")
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"   ❌ {var}: 未设置")
            all_good = False
    
    print("\n📋 可选的环境变量:")
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"   ⚠️ {var}: 未设置 (使用默认值)")
    
    return all_good

async def main():
    """主测试函数"""
    print("🏮 LiveKit风水AI助手 - 多模态功能测试")
    print("=" * 50)
    
    # 测试环境变量
    env_ok = test_environment_variables()
    
    # 测试目录结构
    knowledge_ok = test_knowledge_images_directory()
    screenshot_ok = test_server_screenshots_directory()
    
    # 测试API
    if env_ok:
        deepseek_ok = await test_deepseek_vision_api()
    else:
        print("\n⚠️ 跳过DeepSeek API测试 (环境变量未配置)")
        deepseek_ok = False
    
    unsplash_ok = await test_unsplash_api()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    print(f"   环境变量配置: {'✅' if env_ok else '❌'}")
    print(f"   知识库图片目录: {'✅' if knowledge_ok else '❌'}")
    print(f"   服务器截图目录: {'✅' if screenshot_ok else '❌'}")
    print(f"   DeepSeek Vision API: {'✅' if deepseek_ok else '❌'}")
    print(f"   Unsplash图片搜索: {'✅' if unsplash_ok else '❌'}")
    
    if all([knowledge_ok, screenshot_ok]) and (deepseek_ok or env_ok):
        print("\n🎉 多模态功能基本就绪！")
        print("💡 建议:")
        print("   1. 在knowledge_images目录中添加参考图片")
        print("   2. 测试前端图片上传功能")
        print("   3. 验证DeepSeek Vision API的图片分析效果")
    else:
        print("\n⚠️ 部分功能需要进一步配置")

if __name__ == "__main__":
    asyncio.run(main())
